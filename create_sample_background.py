#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء خلفية تجريبية للاختبار
"""

from PIL import Image, ImageDraw
import os

def create_sample_background():
    """إنشاء خلفية تجريبية بتدرج لوني"""
    
    # إنشاء صورة بحجم 1080x1080
    size = (1080, 1080)
    img = Image.new('RGB', size)
    draw = ImageDraw.Draw(img)
    
    # إنشاء تدرج لوني من الأزرق الداكن إلى الأزرق الفاتح
    for y in range(size[1]):
        # حساب نسبة التدرج
        ratio = y / size[1]
        
        # ألوان التدرج (من الأزرق الداكن إلى الأزرق الفاتح)
        start_color = (20, 40, 80)   # أزرق داكن
        end_color = (60, 120, 180)   # أزرق فاتح
        
        # حساب اللون الحالي
        r = int(start_color[0] + (end_color[0] - start_color[0]) * ratio)
        g = int(start_color[1] + (end_color[1] - start_color[1]) * ratio)
        b = int(start_color[2] + (end_color[2] - start_color[2]) * ratio)
        
        # رسم خط أفقي بهذا اللون
        draw.line([(0, y), (size[0], y)], fill=(r, g, b))
    
    # إضافة نمط هندسي بسيط (دوائر شفافة)
    for i in range(5):
        for j in range(5):
            x = (i + 1) * size[0] // 6
            y = (j + 1) * size[1] // 6
            radius = 30
            
            # رسم دائرة شفافة
            bbox = [x - radius, y - radius, x + radius, y + radius]
            draw.ellipse(bbox, outline=(255, 255, 255, 50), width=2)
    
    # حفظ الصورة
    os.makedirs('backgrounds', exist_ok=True)
    output_path = 'backgrounds/sample_background.png'
    img.save(output_path, 'PNG')
    
    print(f"تم إنشاء خلفية تجريبية: {output_path}")
    return output_path

def create_islamic_pattern_background():
    """إنشاء خلفية بنمط إسلامي بسيط"""
    
    size = (1080, 1080)
    img = Image.new('RGB', size, color=(40, 60, 90))  # خلفية زرقاء داكنة
    draw = ImageDraw.Draw(img)
    
    # رسم نمط هندسي إسلامي بسيط
    pattern_size = 120
    color = (80, 120, 160, 100)  # لون شفاف
    
    for x in range(0, size[0], pattern_size):
        for y in range(0, size[1], pattern_size):
            # رسم معين
            points = [
                (x + pattern_size//2, y),
                (x + pattern_size, y + pattern_size//2),
                (x + pattern_size//2, y + pattern_size),
                (x, y + pattern_size//2)
            ]
            draw.polygon(points, outline=(255, 255, 255, 30), width=1)
            
            # رسم دائرة في المنتصف
            center_x = x + pattern_size // 2
            center_y = y + pattern_size // 2
            radius = pattern_size // 6
            bbox = [center_x - radius, center_y - radius, 
                   center_x + radius, center_y + radius]
            draw.ellipse(bbox, outline=(255, 255, 255, 40), width=1)
    
    # حفظ الصورة
    os.makedirs('backgrounds', exist_ok=True)
    output_path = 'backgrounds/islamic_pattern.png'
    img.save(output_path, 'PNG')
    
    print(f"تم إنشاء خلفية بنمط إسلامي: {output_path}")
    return output_path

def create_gradient_backgrounds():
    """إنشاء عدة خلفيات بتدرجات مختلفة"""
    
    gradients = [
        # (اسم الملف، لون البداية، لون النهاية)
        ('gradient_blue.png', (20, 40, 80), (60, 120, 180)),
        ('gradient_green.png', (20, 60, 40), (60, 140, 80)),
        ('gradient_purple.png', (40, 20, 80), (100, 60, 160)),
        ('gradient_gold.png', (80, 60, 20), (180, 140, 60)),
        ('gradient_sunset.png', (80, 40, 20), (180, 100, 60))
    ]
    
    os.makedirs('backgrounds', exist_ok=True)
    
    for filename, start_color, end_color in gradients:
        size = (1080, 1080)
        img = Image.new('RGB', size)
        draw = ImageDraw.Draw(img)
        
        for y in range(size[1]):
            ratio = y / size[1]
            r = int(start_color[0] + (end_color[0] - start_color[0]) * ratio)
            g = int(start_color[1] + (end_color[1] - start_color[1]) * ratio)
            b = int(start_color[2] + (end_color[2] - start_color[2]) * ratio)
            
            draw.line([(0, y), (size[0], y)], fill=(r, g, b))
        
        output_path = f'backgrounds/{filename}'
        img.save(output_path, 'PNG')
        print(f"تم إنشاء: {output_path}")

if __name__ == "__main__":
    print("🎨 إنشاء خلفيات تجريبية...")
    
    # إنشاء خلفيات مختلفة
    create_sample_background()
    create_islamic_pattern_background()
    create_gradient_backgrounds()
    
    print("✅ تم إنشاء جميع الخلفيات التجريبية بنجاح!")
    print("📁 تحقق من مجلد 'backgrounds' لرؤية الخلفيات المُنشأة")
