import json
import os
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from typing import Dict, List, Tuple, Optional
import random
import math

class TemplateManager:
    """مدير القوالب المخصصة للتصميم"""
    
    def __init__(self, templates_file: str = "data/templates.json"):
        self.templates_file = templates_file
        self.templates = self._load_templates()
        self.color_schemes = self._get_color_schemes()
        self.design_patterns = self._get_design_patterns()
    
    def _load_templates(self) -> Dict:
        """تحميل قوالب التصميم"""
        try:
            if os.path.exists(self.templates_file):
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل القوالب: {e}")
        
        return self._get_default_templates()
    
    def _get_default_templates(self) -> Dict:
        """القوالب الافتراضية"""
        return {
            "classic": {
                "name": "كلاسيكي",
                "description": "تصميم تقليدي أنيق",
                "layout": "centered",
                "text_alignment": "center",
                "background_style": "gradient",
                "decorations": ["border", "corners"],
                "font_sizes": {"verse": 48, "surah": 32, "phrase": 36}
            },
            "modern": {
                "name": "عصري",
                "description": "تصميم عصري ومبسط",
                "layout": "asymmetric",
                "text_alignment": "right",
                "background_style": "solid_with_pattern",
                "decorations": ["geometric_shapes", "lines"],
                "font_sizes": {"verse": 52, "surah": 28, "phrase": 40}
            },
            "elegant": {
                "name": "أنيق",
                "description": "تصميم راقي وفخم",
                "layout": "layered",
                "text_alignment": "center",
                "background_style": "textured",
                "decorations": ["ornaments", "frame", "shadows"],
                "font_sizes": {"verse": 46, "surah": 30, "phrase": 38}
            },
            "minimalist": {
                "name": "بسيط",
                "description": "تصميم بسيط ونظيف",
                "layout": "simple",
                "text_alignment": "center",
                "background_style": "clean_gradient",
                "decorations": ["subtle_border"],
                "font_sizes": {"verse": 50, "surah": 26, "phrase": 34}
            },
            "artistic": {
                "name": "فني",
                "description": "تصميم إبداعي وفني",
                "layout": "creative",
                "text_alignment": "dynamic",
                "background_style": "artistic_blend",
                "decorations": ["artistic_elements", "color_splash"],
                "font_sizes": {"verse": 44, "surah": 34, "phrase": 42}
            }
        }
    
    def _get_color_schemes(self) -> Dict:
        """مخططات الألوان المختلفة"""
        return {
            "blue_gradient": {
                "name": "تدرج أزرق",
                "primary": (20, 40, 80),
                "secondary": (60, 120, 180),
                "accent": (255, 215, 0),
                "text": (255, 255, 255),
                "text_shadow": (0, 0, 0, 128)
            },
            "green_nature": {
                "name": "طبيعة خضراء",
                "primary": (20, 60, 40),
                "secondary": (60, 140, 80),
                "accent": (255, 223, 0),
                "text": (255, 255, 255),
                "text_shadow": (0, 0, 0, 150)
            },
            "purple_royal": {
                "name": "بنفسجي ملكي",
                "primary": (40, 20, 80),
                "secondary": (100, 60, 160),
                "accent": (255, 215, 0),
                "text": (255, 255, 255),
                "text_shadow": (0, 0, 0, 140)
            },
            "gold_luxury": {
                "name": "ذهبي فاخر",
                "primary": (80, 60, 20),
                "secondary": (180, 140, 60),
                "accent": (255, 255, 255),
                "text": (255, 255, 255),
                "text_shadow": (0, 0, 0, 160)
            },
            "sunset_warm": {
                "name": "غروب دافئ",
                "primary": (80, 40, 20),
                "secondary": (180, 100, 60),
                "accent": (255, 200, 100),
                "text": (255, 255, 255),
                "text_shadow": (0, 0, 0, 130)
            },
            "ocean_deep": {
                "name": "محيط عميق",
                "primary": (10, 30, 60),
                "secondary": (30, 80, 120),
                "accent": (100, 200, 255),
                "text": (255, 255, 255),
                "text_shadow": (0, 0, 0, 140)
            }
        }
    
    def _get_design_patterns(self) -> Dict:
        """أنماط التصميم الهندسية"""
        return {
            "islamic_geometric": {
                "name": "هندسي إسلامي",
                "pattern_type": "geometric",
                "complexity": "medium",
                "opacity": 0.1
            },
            "arabesque": {
                "name": "أرابيسك",
                "pattern_type": "floral",
                "complexity": "high",
                "opacity": 0.08
            },
            "calligraphy_bg": {
                "name": "خلفية خطية",
                "pattern_type": "text_based",
                "complexity": "low",
                "opacity": 0.05
            },
            "mandala": {
                "name": "ماندالا",
                "pattern_type": "circular",
                "complexity": "high",
                "opacity": 0.12
            }
        }
    
    def create_background_with_template(self, template_name: str, color_scheme: str, 
                                      size: Tuple[int, int] = (1080, 1080)) -> Image.Image:
        """إنشاء خلفية باستخدام قالب محدد"""
        template = self.templates.get(template_name, self.templates["classic"])
        colors = self.color_schemes.get(color_scheme, self.color_schemes["blue_gradient"])
        
        img = Image.new('RGB', size, colors["primary"])
        draw = ImageDraw.Draw(img)
        
        # تطبيق نمط الخلفية حسب القالب
        background_style = template["background_style"]
        
        if background_style == "gradient":
            img = self._create_gradient_background(img, colors["primary"], colors["secondary"])
        elif background_style == "solid_with_pattern":
            img = self._create_pattern_background(img, colors, "islamic_geometric")
        elif background_style == "textured":
            img = self._create_textured_background(img, colors)
        elif background_style == "clean_gradient":
            img = self._create_clean_gradient(img, colors["primary"], colors["secondary"])
        elif background_style == "artistic_blend":
            img = self._create_artistic_background(img, colors)
        
        # إضافة الزخارف حسب القالب
        decorations = template.get("decorations", [])
        for decoration in decorations:
            img = self._add_decoration(img, decoration, colors)
        
        return img
    
    def _create_gradient_background(self, img: Image.Image, color1: Tuple, color2: Tuple) -> Image.Image:
        """إنشاء خلفية متدرجة"""
        width, height = img.size
        draw = ImageDraw.Draw(img)
        
        for y in range(height):
            ratio = y / height
            r = int(color1[0] + (color2[0] - color1[0]) * ratio)
            g = int(color1[1] + (color2[1] - color1[1]) * ratio)
            b = int(color1[2] + (color2[2] - color1[2]) * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        return img
    
    def _create_pattern_background(self, img: Image.Image, colors: Dict, pattern_name: str) -> Image.Image:
        """إنشاء خلفية بنمط هندسي"""
        width, height = img.size
        draw = ImageDraw.Draw(img)
        
        # إنشاء تدرج أساسي
        img = self._create_gradient_background(img, colors["primary"], colors["secondary"])
        
        # إضافة النمط الهندسي
        pattern_size = 80
        pattern_color = (*colors["accent"][:3], 30)  # شفاف
        
        for x in range(0, width, pattern_size):
            for y in range(0, height, pattern_size):
                if pattern_name == "islamic_geometric":
                    self._draw_islamic_pattern(draw, x, y, pattern_size, pattern_color)
                elif pattern_name == "mandala":
                    self._draw_mandala_pattern(draw, x + pattern_size//2, y + pattern_size//2, 
                                             pattern_size//3, pattern_color)
        
        return img
    
    def _create_textured_background(self, img: Image.Image, colors: Dict) -> Image.Image:
        """إنشاء خلفية محكمة"""
        # إنشاء تدرج أساسي
        img = self._create_gradient_background(img, colors["primary"], colors["secondary"])
        
        # إضافة ضوضاء للحصول على ملمس
        width, height = img.size
        pixels = img.load()
        
        for x in range(0, width, 3):
            for y in range(0, height, 3):
                noise = random.randint(-15, 15)
                r, g, b = pixels[x, y]
                r = max(0, min(255, r + noise))
                g = max(0, min(255, g + noise))
                b = max(0, min(255, b + noise))
                pixels[x, y] = (r, g, b)
        
        # تطبيق مرشح تنعيم خفيف
        img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        return img
    
    def _create_clean_gradient(self, img: Image.Image, color1: Tuple, color2: Tuple) -> Image.Image:
        """إنشاء تدرج نظيف وبسيط"""
        width, height = img.size
        draw = ImageDraw.Draw(img)
        
        # تدرج قطري
        for i in range(max(width, height)):
            ratio = i / max(width, height)
            r = int(color1[0] + (color2[0] - color1[0]) * ratio)
            g = int(color1[1] + (color2[1] - color1[1]) * ratio)
            b = int(color1[2] + (color2[2] - color1[2]) * ratio)
            
            # رسم خط قطري
            draw.line([(i, 0), (0, i)], fill=(r, g, b), width=2)
            if i < width:
                draw.line([(width-1, i), (i, height-1)], fill=(r, g, b), width=2)
        
        return img
    
    def _create_artistic_background(self, img: Image.Image, colors: Dict) -> Image.Image:
        """إنشاء خلفية فنية إبداعية"""
        width, height = img.size
        draw = ImageDraw.Draw(img)
        
        # خلفية متدرجة
        img = self._create_gradient_background(img, colors["primary"], colors["secondary"])
        
        # إضافة دوائر فنية
        for _ in range(8):
            x = random.randint(0, width)
            y = random.randint(0, height)
            radius = random.randint(50, 200)
            alpha = random.randint(20, 60)
            
            # إنشاء طبقة شفافة للدائرة
            circle_img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            circle_draw = ImageDraw.Draw(circle_img)
            
            color = (*colors["accent"][:3], alpha)
            circle_draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                              fill=color, outline=None)
            
            # دمج الدائرة مع الصورة الأساسية
            img = Image.alpha_composite(img.convert('RGBA'), circle_img).convert('RGB')
        
        return img
    
    def _draw_islamic_pattern(self, draw: ImageDraw.Draw, x: int, y: int, size: int, color: Tuple):
        """رسم نمط إسلامي هندسي"""
        center_x = x + size // 2
        center_y = y + size // 2
        
        # رسم نجمة ثمانية
        points = []
        for i in range(8):
            angle = i * math.pi / 4
            if i % 2 == 0:
                radius = size // 3
            else:
                radius = size // 6
            
            px = center_x + int(radius * math.cos(angle))
            py = center_y + int(radius * math.sin(angle))
            points.append((px, py))
        
        if len(points) >= 3:
            draw.polygon(points, outline=color[:3], width=1)
    
    def _draw_mandala_pattern(self, draw: ImageDraw.Draw, center_x: int, center_y: int, 
                            radius: int, color: Tuple):
        """رسم نمط ماندالا"""
        # رسم دوائر متداخلة
        for r in range(radius, 0, -10):
            draw.ellipse([center_x-r, center_y-r, center_x+r, center_y+r], 
                        outline=color[:3], width=1)
        
        # رسم خطوط شعاعية
        for i in range(8):
            angle = i * math.pi / 4
            end_x = center_x + int(radius * math.cos(angle))
            end_y = center_y + int(radius * math.sin(angle))
            draw.line([(center_x, center_y), (end_x, end_y)], fill=color[:3], width=1)
    
    def _add_decoration(self, img: Image.Image, decoration: str, colors: Dict) -> Image.Image:
        """إضافة زخرفة للصورة"""
        width, height = img.size
        draw = ImageDraw.Draw(img)
        
        if decoration == "border":
            # إضافة إطار
            border_width = 10
            border_color = colors["accent"][:3]
            draw.rectangle([0, 0, width-1, height-1], outline=border_color, width=border_width)
        
        elif decoration == "corners":
            # إضافة زخارف في الزوايا
            corner_size = 50
            corner_color = colors["accent"][:3]
            
            # الزاوية العلوية اليسرى
            draw.arc([10, 10, corner_size, corner_size], 0, 90, fill=corner_color, width=3)
            # الزاوية العلوية اليمنى
            draw.arc([width-corner_size, 10, width-10, corner_size], 90, 180, fill=corner_color, width=3)
            # الزاوية السفلية اليسرى
            draw.arc([10, height-corner_size, corner_size, height-10], 270, 360, fill=corner_color, width=3)
            # الزاوية السفلية اليمنى
            draw.arc([width-corner_size, height-corner_size, width-10, height-10], 180, 270, fill=corner_color, width=3)
        
        elif decoration == "geometric_shapes":
            # إضافة أشكال هندسية
            for _ in range(5):
                x = random.randint(50, width-50)
                y = random.randint(50, height-50)
                size = random.randint(20, 40)
                alpha = 30
                
                shape_color = (*colors["accent"][:3], alpha)
                draw.regular_polygon((x, y, size), 6, outline=shape_color[:3], width=2)
        
        return img
    
    def get_template_list(self) -> List[Dict]:
        """الحصول على قائمة القوالب المتاحة"""
        templates_list = []
        for key, template in self.templates.items():
            templates_list.append({
                'id': key,
                'name': template['name'],
                'description': template['description'],
                'preview_url': f'/api/template_preview/{key}'
            })
        return templates_list
    
    def get_color_schemes_list(self) -> List[Dict]:
        """الحصول على قائمة مخططات الألوان"""
        schemes_list = []
        for key, scheme in self.color_schemes.items():
            schemes_list.append({
                'id': key,
                'name': scheme['name'],
                'colors': {
                    'primary': scheme['primary'],
                    'secondary': scheme['secondary'],
                    'accent': scheme['accent']
                }
            })
        return schemes_list
