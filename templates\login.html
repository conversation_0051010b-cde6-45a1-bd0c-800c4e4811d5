<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - روبوت نشر الآيات القرآنية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header .icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 1rem;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e0e0e0;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }
        
        .form-floating label {
            color: #666;
            font-weight: 500;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .alert {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .forgot-password a:hover {
            color: #764ba2;
        }
        
        .features-list {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: #666;
            font-size: 0.9rem;
        }
        
        .feature-item i {
            color: #667eea;
            margin-left: 10px;
            width: 20px;
        }
        
        .demo-credentials {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
        }
        
        .demo-credentials h6 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .demo-credentials p {
            margin: 5px 0;
            font-family: monospace;
            color: #666;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .login-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
        
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="icon">
                <i class="fas fa-mosque"></i>
            </div>
            <h1>روبوت نشر الآيات القرآنية</h1>
            <p>منصة ذكية لنشر المحتوى الإسلامي</p>
        </div>
        
        {% if error %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ error }}
        </div>
        {% endif %}
        
        <form method="POST" id="loginForm">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                <label for="username">
                    <i class="fas fa-user me-2"></i>
                    اسم المستخدم
                </label>
            </div>
            
            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                <label for="password">
                    <i class="fas fa-lock me-2"></i>
                    كلمة المرور
                </label>
            </div>
            
            <button type="submit" class="btn btn-login">
                <span class="login-text">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </span>
                <span class="loading">
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    جاري التحقق...
                </span>
            </button>
        </form>
        
        <div class="demo-credentials">
            <h6><i class="fas fa-info-circle me-2"></i>بيانات تجريبية</h6>
            <p><strong>المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>
        
        <div class="features-list">
            <div class="feature-item">
                <i class="fas fa-robot"></i>
                <span>ذكاء اصطناعي متقدم</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-chart-line"></i>
                <span>تحليلات مفصلة</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-users"></i>
                <span>إدارة متعددة المستخدمين</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                <span>أمان متقدم</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-palette"></i>
                <span>قوالب تصميم متنوعة</span>
            </div>
        </div>
        
        <div class="forgot-password">
            <a href="#" onclick="showForgotPassword()">
                <i class="fas fa-question-circle me-1"></i>
                نسيت كلمة المرور؟
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const loginText = document.querySelector('.login-text');
            const loading = document.querySelector('.loading');
            
            loginText.style.display = 'none';
            loading.classList.add('show');
            
            // إعادة تفعيل النص بعد 10 ثواني في حالة عدم الاستجابة
            setTimeout(() => {
                loginText.style.display = 'inline';
                loading.classList.remove('show');
            }, 10000);
        });
        
        function showForgotPassword() {
            alert('يرجى التواصل مع المشرف لإعادة تعيين كلمة المرور');
        }
        
        // تركيز تلقائي على حقل اسم المستخدم
        document.getElementById('username').focus();
    </script>
</body>
</html>
