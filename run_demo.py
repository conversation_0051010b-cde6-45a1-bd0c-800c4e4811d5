#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تجريبي للنظام
"""

import os
import time
from modules.quran_manager import QuranManager
from modules.image_generator import ImageGenerator

def demo_image_generation():
    """عرض توضيحي لتوليد الصور"""
    print("🚀 بدء العرض التوضيحي لتوليد الصور")
    print("=" * 50)
    
    # تهيئة المكونات
    print("📚 تهيئة مدير القرآن...")
    quran_manager = QuranManager()
    
    print("🎨 تهيئة مولد الصور...")
    image_generator = ImageGenerator()
    
    # إنشاء عدة صور تجريبية
    for i in range(3):
        print(f"\n🖼️ إنشاء الصورة رقم {i+1}...")
        
        # الحصول على آية عشوائية
        verse = quran_manager.get_random_verse()
        if not verse:
            print("❌ لم يتم العثور على آية")
            continue
        
        # الحصول على عبارة إلهامية
        phrase = quran_manager.get_random_inspirational_phrase()
        
        print(f"📖 الآية: {verse['surah']} - آية {verse['verse_number']}")
        print(f"💭 العبارة: {phrase}")
        
        # توليد الصورة
        try:
            image_path = image_generator.generate_verse_image(verse, phrase)
            if image_path and os.path.exists(image_path):
                print(f"✅ تم إنشاء الصورة: {image_path}")
            else:
                print("❌ فشل في إنشاء الصورة")
        except Exception as e:
            print(f"❌ خطأ في إنشاء الصورة: {e}")
        
        # انتظار قصير
        time.sleep(1)
    
    # عرض الإحصائيات
    print(f"\n📊 إحصائيات الآيات:")
    stats = quran_manager.get_verse_stats()
    print(f"   - إجمالي الآيات: {stats['total_verses']}")
    print(f"   - الآيات المستخدمة: {stats['used_verses']}")
    print(f"   - الآيات المتبقية: {stats['remaining_verses']}")
    print(f"   - نسبة الاستخدام: {stats['usage_percentage']:.1f}%")
    
    # عرض الصور المولدة
    generated_dir = "generated_images"
    if os.path.exists(generated_dir):
        images = [f for f in os.listdir(generated_dir) if f.endswith('.png')]
        print(f"\n🖼️ الصور المولدة ({len(images)} صورة):")
        for img in images[-5:]:  # عرض آخر 5 صور
            print(f"   - {img}")
    
    print("\n🎉 انتهى العرض التوضيحي!")
    print("📁 تحقق من مجلد 'generated_images' لرؤية الصور المُنشأة")

def demo_quran_manager():
    """عرض توضيحي لمدير القرآن"""
    print("\n📚 عرض توضيحي لمدير القرآن")
    print("=" * 30)
    
    manager = QuranManager()
    
    # عرض آيات حسب المواضيع
    themes = ["الصبر", "الرزق", "الأمل", "التوبة"]
    
    for theme in themes:
        print(f"\n🔍 البحث عن آيات حول موضوع: {theme}")
        verse = manager.get_verse_by_theme(theme)
        if verse:
            print(f"✅ وُجدت آية: {verse['surah']} - آية {verse['verse_number']}")
            print(f"   النص: {verse['text'][:80]}...")
        else:
            print(f"❌ لم توجد آيات حول {theme}")
    
    # عرض العبارات الإلهامية
    print(f"\n💭 عبارات إلهامية:")
    for i in range(3):
        phrase = manager.get_random_inspirational_phrase()
        print(f"   {i+1}. {phrase}")

if __name__ == "__main__":
    print("🕌 مرحباً بك في روبوت نشر الآيات القرآنية")
    print("=" * 60)
    
    try:
        # تشغيل العروض التوضيحية
        demo_quran_manager()
        demo_image_generation()
        
        print("\n" + "=" * 60)
        print("✨ تم تشغيل جميع العروض التوضيحية بنجاح!")
        print("🚀 النظام جاهز للاستخدام")
        
    except Exception as e:
        print(f"\n❌ خطأ في العرض التوضيحي: {e}")
        print("🔧 يرجى التحقق من الإعدادات والمتطلبات")
