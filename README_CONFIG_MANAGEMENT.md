# 🕌 بوت نشر الآيات القرآنية - نظام إدارة الإعدادات المحسن

## ✨ الميزات الجديدة

### 🔧 إدارة الإعدادات المتقدمة
- **عرض الإعدادات الحقيقية**: يعرض البوت الآن الإعدادات الفعلية من ملف `.env` بدلاً من الإعدادات الوهمية
- **تعديل الإعدادات من البوت**: إمكانية تعديل جميع الإعدادات مباشرة من خلال أوامر البوت
- **إخفاء البيانات الحساسة**: كلمات المرور ومفاتيح API مخفية جزئياً للأمان
- **تاريخ التغييرات**: تسجيل جميع التغييرات مع الوقت ومعرف المستخدم
- **التحقق من صحة الإعدادات**: فحص تلقائي للإعدادات وعرض الأخطاء والتحذيرات

### 🛡️ تحسينات الأمان والاستقرار
- **معالجة أخطاء Telegram**: حل مشكلة "Query is too old" في أزرار البوت
- **نسخ احتياطية تلقائية**: حفظ تاريخ التغييرات في `data/config_history.json`
- **إعادة تحميل آمنة**: إعادة تحميل الإعدادات بدون إعادة تشغيل البوت

## 🚀 كيفية التشغيل

### الطريقة المحسنة (موصى بها):
```bash
python run_bot.py
```

### الطريقة التقليدية:
```bash
python telegram_bot_controller.py
```

## 📱 كيفية استخدام النظام الجديد

### 1. الوصول لإدارة الإعدادات
1. ابدأ البوت: `/start`
2. اضغط: "📱 إدارة المنصات"
3. اضغط: "⚙️ إعداد المنصات"

### 2. عرض الإعدادات الحالية
ستظهر لك:
- ✅ المنصات المفعلة
- ❌ المنصات المعطلة
- 🔑 مفاتيح API (مخفية جزئياً)
- ⚠️ التحذيرات والأخطاء

### 3. تعديل الإعدادات

#### تفعيل/تعطيل المنصات:
```
/set_enable_telegram true
/set_enable_instagram false
/set_enable_facebook true
```

#### إعداد Telegram:
```
/set_telegram_bot_token 123456:ABC-DEF...
/set_telegram_channel_id @mychannel
```

#### إعداد Instagram:
```
/set_instagram_username myusername
/set_instagram_password mypassword
```

#### إعداد Facebook:
```
/set_facebook_access_token EAABwz...
/set_facebook_page_id 123456789
```

#### إعداد Gemini AI:
```
/set_gemini_api_key AIzaSy...
```

### 4. تطبيق التغييرات
بعد تعديل الإعدادات:
1. اضغط: "🔄 إعادة تحميل الإعدادات"
2. تحقق من النتيجة في "📋 ملخص الإعدادات"

## 📊 الأوامر الجديدة

### أوامر الإدارة:
- `/config_summary` - عرض ملخص شامل للإعدادات
- `/config_history` - عرض تاريخ آخر 10 تغييرات
- `/platforms` - إدارة المنصات

### أوامر تعديل Telegram:
- `/set_enable_telegram true/false`
- `/set_telegram_bot_token <token>`
- `/set_telegram_channel_id <id>`

### أوامر تعديل Instagram:
- `/set_enable_instagram true/false`
- `/set_instagram_username <username>`
- `/set_instagram_password <password>`

### أوامر تعديل Facebook:
- `/set_enable_facebook true/false`
- `/set_facebook_access_token <token>`
- `/set_facebook_page_id <id>`

### أوامر تعديل Gemini:
- `/set_gemini_api_key <key>`

## 🔍 مثال عملي كامل

### إعداد النظام من الصفر:

1. **تفعيل Telegram:**
```
/set_enable_telegram true
/set_telegram_bot_token **********************************************
/set_telegram_channel_id @mychannel
```

2. **إعداد Gemini AI:**
```
/set_gemini_api_key AIzaSyA9sCmQsOZIPKRKX14aMJsC8Mt7IFPsYE8
```

3. **تطبيق التغييرات:**
- اضغط: "🔄 إعادة تحميل الإعدادات"

4. **التحقق من النتيجة:**
- اضغط: "📋 ملخص الإعدادات"

## 📁 الملفات الجديدة

- `modules/config_manager.py` - مدير الإعدادات المتقدم
- `run_bot.py` - ملف التشغيل المحسن
- `test_config_manager.py` - اختبار النظام
- `CONFIG_MANAGEMENT_GUIDE.md` - دليل مفصل
- `data/config_history.json` - تاريخ التغييرات

## 🛠️ استكشاف الأخطاء

### المشاكل الشائعة:

#### "❌ فشل في تحديث الإعدادات"
- تحقق من صلاحيات الكتابة على ملف `.env`
- تأكد من صحة تنسيق القيم المدخلة

#### "Query is too old" في Telegram
- تم حل هذه المشكلة في النسخة الجديدة
- النظام يتجاهل الأخطاء القديمة تلقائياً

#### "لا توجد منصات مفعلة"
- فعل منصة واحدة على الأقل باستخدام أوامر `set_enable_*`
- تأكد من إعادة تحميل الإعدادات

### فحص السجلات:
```bash
# عرض آخر 50 سطر من السجل
tail -50 bot_controller.log

# متابعة السجل مباشرة
tail -f bot_controller.log
```

## 📈 الإحصائيات والمراقبة

### عرض الإحصائيات:
- `/stats` - إحصائيات النظام
- `/status` - حالة المكونات
- `/config_summary` - ملخص الإعدادات

### مراقبة التغييرات:
- `/config_history` - آخر التغييرات
- فحص ملف `data/config_history.json`

## 🔐 الأمان

### حماية البيانات:
- كلمات المرور مخفية في العرض
- مفاتيح API مقنعة جزئياً
- تسجيل التغييرات بدون كشف القيم الحساسة

### النسخ الاحتياطية:
- تاريخ كامل للتغييرات في `data/config_history.json`
- إمكانية استرجاع القيم السابقة يدوياً

## 💡 نصائح للاستخدام الأمثل

1. **استخدم ملخص الإعدادات بانتظام** للتحقق من صحة الإعدادات
2. **راجع تاريخ التغييرات** عند حدوث مشاكل
3. **اختبر المنصات** بعد كل تغيير مهم
4. **احتفظ بنسخة احتياطية** من ملف `.env`
5. **استخدم إعادة التحميل** بعد كل مجموعة تغييرات

## 🆘 الدعم

للحصول على المساعدة:
1. راجع ملف `CONFIG_MANAGEMENT_GUIDE.md` للتفاصيل
2. فحص سجلات البوت في `bot_controller.log`
3. استخدم `/help` في البوت للأوامر المتاحة
4. تحقق من ملف `data/config_history.json` لتاريخ التغييرات
