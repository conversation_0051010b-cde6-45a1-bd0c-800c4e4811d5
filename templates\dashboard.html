<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - روبوت نشر الآيات القرآنية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .icon {
            font-size: 3rem;
            opacity: 0.8;
            margin-bottom: 15px;
        }
        
        .stat-card .number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-card .label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .section-title {
            color: #333;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .platform-status {
            display: flex;
            align-items: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        
        .platform-status.active {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }
        
        .platform-status.inactive {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
            color: white;
        }
        
        .platform-icon {
            font-size: 1.5rem;
            margin-left: 15px;
            width: 30px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .recent-posts {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .post-item {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .post-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .post-performance {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .performance-excellent {
            background: #4CAF50;
            color: white;
        }
        
        .performance-good {
            background: #2196F3;
            color: white;
        }
        
        .performance-average {
            background: #FF9800;
            color: white;
        }
        
        .performance-poor {
            background: #f44336;
            color: white;
        }
        
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 15px 0;
        }
        
        .navbar-brand {
            color: white !important;
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
        }
        
        .nav-link:hover {
            color: white !important;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
        }
        
        .modal-custom .modal-content {
            border-radius: 15px;
            border: none;
        }
        
        .modal-custom .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .form-control-custom {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control-custom:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-mosque me-2"></i>
                روبوت نشر الآيات القرآنية
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#dashboard"><i class="fas fa-tachometer-alt me-1"></i> لوحة التحكم</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#content"><i class="fas fa-edit me-1"></i> إدارة المحتوى</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#analytics"><i class="fas fa-chart-bar me-1"></i> التحليلات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#settings"><i class="fas fa-cog me-1"></i> الإعدادات</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i> {{ username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#profile"><i class="fas fa-user-edit me-2"></i> الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Dashboard -->
    <div class="container-fluid">
        <div class="dashboard-container">
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="number" id="totalPosts">{{ stats.total_posts or 0 }}</div>
                        <div class="label">إجمالي المنشورات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="number" id="successRate">{{ stats.success_rate or 0 }}%</div>
                        <div class="label">معدل النجاح</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="number" id="avgEngagement">{{ stats.avg_engagement or 0 }}</div>
                        <div class="label">متوسط التفاعل</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="number" id="nextPost">{{ next_post_time or "قريباً" }}</div>
                        <div class="label">المنشور التالي</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="content-section">
                        <h3 class="section-title">
                            <i class="fas fa-bolt me-2"></i>
                            إجراءات سريعة
                        </h3>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-custom w-100" onclick="publishNow()">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    نشر فوري
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-custom w-100" onclick="previewPost()">
                                    <i class="fas fa-eye me-2"></i>
                                    معاينة المنشور
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-custom w-100" onclick="openContentManager()">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة محتوى
                                </button>
                            </div>
                            <div class="col-md-3 mb-3">
                                <button class="btn btn-custom w-100" onclick="exportReport()">
                                    <i class="fas fa-download me-2"></i>
                                    تصدير تقرير
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-success w-100" id="autoPublishBtn" onclick="toggleAutoPublish()">
                                    <i class="fas fa-play me-2" id="autoPublishIcon"></i>
                                    <span id="autoPublishText">تشغيل النشر التلقائي</span>
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-info w-100" onclick="openScheduleSettings()">
                                    <i class="fas fa-clock me-2"></i>
                                    إعدادات التوقيت
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-warning w-100" onclick="checkSchedulerStatus()">
                                    <i class="fas fa-info-circle me-2"></i>
                                    حالة الجدولة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Platform Status and Performance Chart -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="content-section">
                        <h3 class="section-title">
                            <i class="fas fa-share-alt me-2"></i>
                            حالة المنصات
                        </h3>
                        <div id="platformStatus">
                            <!-- Platform status will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="content-section">
                        <h3 class="section-title">
                            <i class="fas fa-chart-area me-2"></i>
                            أداء المنشورات
                        </h3>
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Posts and Content Insights -->
            <div class="row">
                <div class="col-md-6">
                    <div class="content-section">
                        <h3 class="section-title">
                            <i class="fas fa-history me-2"></i>
                            المنشورات الأخيرة
                        </h3>
                        <div class="recent-posts" id="recentPosts">
                            <!-- Recent posts will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="content-section">
                        <h3 class="section-title">
                            <i class="fas fa-lightbulb me-2"></i>
                            رؤى المحتوى
                        </h3>
                        <div id="contentInsights">
                            <!-- Content insights will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner"></div>
                    <h5>جاري المعالجة...</h5>
                    <p class="text-muted">يرجى الانتظار</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Settings Modal -->
    <div class="modal fade modal-custom" id="scheduleModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-clock me-2"></i>
                        إعدادات توقيت النشر التلقائي
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="scheduleForm">
                        <div class="mb-3">
                            <label for="publishInterval" class="form-label">فترة النشر (بالساعات)</label>
                            <select class="form-control form-control-custom" id="publishInterval">
                                <option value="1">كل ساعة</option>
                                <option value="2">كل ساعتين</option>
                                <option value="3" selected>كل 3 ساعات</option>
                                <option value="4">كل 4 ساعات</option>
                                <option value="6">كل 6 ساعات</option>
                                <option value="8">كل 8 ساعات</option>
                                <option value="12">كل 12 ساعة</option>
                                <option value="24">كل 24 ساعة</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">معاينة التوقيت</label>
                            <div class="alert alert-info" id="schedulePreview">
                                النشر التالي سيكون خلال 3 ساعات
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="immediateStart">
                                <label class="form-check-label" for="immediateStart">
                                    بدء النشر فوراً عند التفعيل
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-custom" onclick="saveScheduleSettings()">حفظ الإعدادات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scheduler Status Modal -->
    <div class="modal fade modal-custom" id="statusModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>
                        حالة نظام الجدولة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="schedulerStatusContent">
                        <!-- Status content will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
