import schedule
import time
import threading
from datetime import datetime, timedelta
from typing import Callable, Optional
import logging

class ContentScheduler:
    """جدولة المحتوى التلقائية"""
    
    def __init__(self, publish_function: Callable, interval_hours: int = 4):
        self.publish_function = publish_function
        self.interval_hours = interval_hours
        self.is_running = False
        self.scheduler_thread = None
        
        # إعداد نظام السجلات
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('scheduler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def schedule_posts(self):
        """جدولة المنشورات كل فترة محددة"""
        # جدولة النشر كل 4 ساعات
        schedule.every(self.interval_hours).hours.do(self._run_publish_job)
        
        # جدولة نشر فوري عند بدء التشغيل (اختياري)
        # schedule.every().minute.do(self._run_publish_job).tag('immediate')
        
        self.logger.info(f"تم جدولة النشر كل {self.interval_hours} ساعات")
    
    def _run_publish_job(self):
        """تشغيل مهمة النشر"""
        try:
            self.logger.info("بدء مهمة النشر المجدولة...")
            success = self.publish_function()
            
            if success:
                self.logger.info("تم تنفيذ مهمة النشر بنجاح")
            else:
                self.logger.error("فشل في تنفيذ مهمة النشر")
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ مهمة النشر: {e}")
    
    def start_scheduler(self):
        """بدء تشغيل الجدولة"""
        if self.is_running:
            self.logger.warning("الجدولة تعمل بالفعل")
            return
        
        self.schedule_posts()
        self.is_running = True
        
        # تشغيل الجدولة في خيط منفصل
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("تم بدء تشغيل نظام الجدولة")
    
    def _run_scheduler(self):
        """تشغيل حلقة الجدولة"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
            except Exception as e:
                self.logger.error(f"خطأ في حلقة الجدولة: {e}")
                time.sleep(60)
    
    def stop_scheduler(self):
        """إيقاف الجدولة"""
        self.is_running = False
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        self.logger.info("تم إيقاف نظام الجدولة")
    
    def get_next_run_time(self) -> Optional[datetime]:
        """الحصول على وقت التشغيل التالي"""
        jobs = schedule.get_jobs()
        if jobs:
            next_run = min(job.next_run for job in jobs)
            return next_run
        return None
    
    def get_scheduler_status(self) -> dict:
        """الحصول على حالة الجدولة"""
        next_run = self.get_next_run_time()
        
        return {
            'is_running': self.is_running,
            'interval_hours': self.interval_hours,
            'next_run': next_run.isoformat() if next_run else None,
            'jobs_count': len(schedule.get_jobs()),
            'thread_alive': self.scheduler_thread.is_alive() if self.scheduler_thread else False
        }
    
    def run_immediate_post(self):
        """تشغيل نشر فوري"""
        self.logger.info("تشغيل نشر فوري...")
        self._run_publish_job()
    
    def update_interval(self, new_interval_hours: int):
        """تحديث فترة النشر"""
        if self.is_running:
            self.stop_scheduler()
            self.interval_hours = new_interval_hours
            self.start_scheduler()
        else:
            self.interval_hours = new_interval_hours
        
        self.logger.info(f"تم تحديث فترة النشر إلى {new_interval_hours} ساعات")


class FlaskScheduler:
    """جدولة مخصصة للعمل مع Flask على Render"""

    def __init__(self, publish_function: Callable, interval_hours: int = 4):
        self.publish_function = publish_function
        self.interval_hours = interval_hours
        self.last_run = None
        self.is_enabled = True
        self.state_file = 'data/scheduler_state.json'

        # إعداد نظام السجلات
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

        # تحميل الحالة المحفوظة
        self._load_state()
    
    def _load_state(self):
        """تحميل حالة الجدولة من الملف"""
        try:
            import json
            import os

            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)

                if state.get('last_run'):
                    self.last_run = datetime.fromisoformat(state['last_run'])
                self.interval_hours = state.get('interval_hours', self.interval_hours)
                self.is_enabled = state.get('is_enabled', True)

                self.logger.info(f"تم تحميل حالة الجدولة: آخر نشر {self.last_run}, الفترة {self.interval_hours}س")
        except Exception as e:
            self.logger.warning(f"فشل في تحميل حالة الجدولة: {e}")

    def _save_state(self):
        """حفظ حالة الجدولة في الملف"""
        try:
            import json
            import os

            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)

            state = {
                'last_run': self.last_run.isoformat() if self.last_run else None,
                'interval_hours': self.interval_hours,
                'is_enabled': self.is_enabled
            }

            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.warning(f"فشل في حفظ حالة الجدولة: {e}")

    def should_run(self) -> bool:
        """فحص ما إذا كان يجب تشغيل النشر"""
        if not self.is_enabled:
            return False

        if self.last_run is None:
            return True

        time_since_last_run = datetime.now() - self.last_run
        return time_since_last_run >= timedelta(hours=self.interval_hours)
    
    def check_and_run(self):
        """فحص وتشغيل النشر إذا كان الوقت مناسباً"""
        if self.should_run():
            try:
                self.logger.info("تشغيل مهمة النشر...")
                success = self.publish_function()

                if success:
                    self.last_run = datetime.now()
                    self._save_state()  # حفظ الحالة بعد النشر الناجح
                    self.logger.info(f"تم النشر بنجاح في {self.last_run}")
                    return True
                else:
                    self.logger.error("فشل في النشر")
                    return False

            except Exception as e:
                self.logger.error(f"خطأ في النشر: {e}")
                return False

        return False

    def enable_scheduler(self):
        """تفعيل الجدولة"""
        self.is_enabled = True
        self._save_state()
        self.logger.info("تم تفعيل نظام الجدولة")

    def disable_scheduler(self):
        """تعطيل الجدولة"""
        self.is_enabled = False
        self._save_state()
        self.logger.info("تم تعطيل نظام الجدولة")

    def update_interval(self, new_interval_hours: int):
        """تحديث فترة النشر"""
        old_interval = self.interval_hours
        self.interval_hours = new_interval_hours
        self._save_state()
        self.logger.info(f"تم تحديث فترة النشر من {old_interval}س إلى {new_interval_hours}س")

    def force_run(self):
        """تشغيل فوري للنشر"""
        try:
            self.logger.info("تشغيل نشر فوري...")
            success = self.publish_function()

            if success:
                self.last_run = datetime.now()
                self._save_state()
                self.logger.info(f"تم النشر الفوري بنجاح في {self.last_run}")
                return True
            else:
                self.logger.error("فشل في النشر الفوري")
                return False

        except Exception as e:
            self.logger.error(f"خطأ في النشر الفوري: {e}")
            return False
    
    def get_time_until_next_run(self) -> Optional[timedelta]:
        """الحصول على الوقت المتبقي للنشر التالي"""
        if self.last_run is None:
            return timedelta(0)
        
        next_run_time = self.last_run + timedelta(hours=self.interval_hours)
        time_until_next = next_run_time - datetime.now()
        
        return time_until_next if time_until_next.total_seconds() > 0 else timedelta(0)
    
    def get_status(self) -> dict:
        """الحصول على حالة الجدولة"""
        time_until_next = self.get_time_until_next_run()

        return {
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'interval_hours': self.interval_hours,
            'time_until_next_run_seconds': int(time_until_next.total_seconds()) if time_until_next else 0,
            'should_run_now': self.should_run(),
            'is_enabled': self.is_enabled,
            'next_run_time': (self.last_run + timedelta(hours=self.interval_hours)).isoformat() if self.last_run else None
        }
