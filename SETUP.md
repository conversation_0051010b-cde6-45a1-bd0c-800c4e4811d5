# 🚀 دليل الإعداد والنشر على Render.com

## 📋 المتطلبات الأساسية

### 1. مفاتيح API المطلوبة:

#### Gemini API (مطلوب):
- اذهب إلى: https://makersuite.google.com/app/apikey
- أنشئ مفتاح API جديد
- احفظ المفتاح لاستخدامه في `GEMINI_API_KEY`

#### Telegram Bot (اختياري):
- تحدث مع @BotFather على Telegram
- أنشئ بوت جديد باستخدام `/newbot`
- احفظ رمز البوت لاستخدامه في `TELEGRAM_BOT_TOKEN`
- أنشئ قناة وأضف البوت كمشرف
- احفظ معرف القناة (مثل: @channel_name) في `TELEGRAM_CHANNEL_ID`

#### Instagram (اختياري):
- استخدم اسم المستخدم وكلمة المرور لحسابك
- تأكد من أن الحساب ليس محمياً بالمصادقة الثنائية

#### Facebook (اختياري):
- اذهب إلى: https://developers.facebook.com/
- أنشئ تطبيق جديد
- احصل على Access Token للصفحة
- احفظ معرف الصفحة

## 🌐 النشر على Render.com

### الخطوة 1: إعداد المستودع
```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin <your-repo-url>
git push -u origin main
```

### الخطوة 2: إنشاء خدمة على Render
1. اذهب إلى https://render.com/
2. أنشئ حساب جديد أو سجل الدخول
3. اضغط على "New +" ثم "Web Service"
4. اربط مستودع GitHub الخاص بك
5. اختر الفرع الرئيسي (main)

### الخطوة 3: إعداد متغيرات البيئة
في إعدادات الخدمة، أضف المتغيرات التالية:

#### متغيرات مطلوبة:
```
GEMINI_API_KEY=your_gemini_api_key_here
ENVIRONMENT=production
PORT=10000
```

#### متغيرات Telegram (إذا كنت تريد تفعيل Telegram):
```
ENABLE_TELEGRAM=true
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHANNEL_ID=@your_channel_username
```

#### متغيرات Instagram (إذا كنت تريد تفعيل Instagram):
```
ENABLE_INSTAGRAM=true
INSTAGRAM_USERNAME=your_username
INSTAGRAM_PASSWORD=your_password
```

#### متغيرات Facebook (إذا كنت تريد تفعيل Facebook):
```
ENABLE_FACEBOOK=true
FACEBOOK_ACCESS_TOKEN=your_access_token
FACEBOOK_PAGE_ID=your_page_id
```

#### متغيرات اختيارية:
```
PUBLISH_INTERVAL_HOURS=4
IMAGE_WIDTH=1080
IMAGE_HEIGHT=1080
VERSE_FONT_SIZE=48
SURAH_FONT_SIZE=32
PHRASE_FONT_SIZE=36
```

### الخطوة 4: إعداد الخطوط والخلفيات

#### إضافة خطوط عربية:
1. ضع ملفات الخطوط (.ttf أو .otf) في مجلد `fonts/`
2. يمكنك تحميل خطوط عربية مجانية من:
   - Google Fonts (Amiri, Cairo, Tajawal)
   - خطوط قرآنية مثل: Uthmanic Hafs, KFGQPC

#### إضافة خلفيات:
1. ضع صور الخلفيات في مجلد `backgrounds/`
2. استخدم صور بدقة عالية (1080x1080 أو أكبر)
3. تأكد من أن الخلفيات مناسبة للنص العربي

### الخطوة 5: النشر
1. اضغط على "Create Web Service"
2. انتظر حتى يكتمل البناء والنشر
3. ستحصل على رابط للتطبيق مثل: `https://your-app-name.onrender.com`

## 🔧 اختبار التطبيق

### فحص الحالة:
- اذهب إلى: `https://your-app-name.onrender.com/`
- تحقق من حالة النظام

### اختبار النشر:
- اذهب إلى: `https://your-app-name.onrender.com/test`
- سيتم نشر آية تجريبية فوراً

### مراقبة السجلات:
- في لوحة تحكم Render، اذهب إلى "Logs"
- راقب سجلات التطبيق للتأكد من عمله بشكل صحيح

## ⚠️ ملاحظات مهمة

### الخطة المجانية في Render:
- ✅ تدعم التشغيل المستمر
- ✅ 750 ساعة مجانية شهرياً
- ✅ مناسبة للمشاريع الصغيرة
- ⚠️ قد تتوقف الخدمة بعد 15 دقيقة من عدم النشاط
- ⚠️ تحتاج إلى ping دوري للحفاظ على التشغيل

### نصائح للحفاظ على التشغيل:
1. استخدم خدمة مثل UptimeRobot لعمل ping كل 5 دقائق
2. اضبط فترة النشر على 4 ساعات أو أقل
3. راقب السجلات بانتظام

### الأمان:
- لا تشارك مفاتيح API مع أحد
- استخدم متغيرات البيئة فقط
- فعّل المصادقة الثنائية على حساباتك

## 🆘 حل المشاكل الشائعة

### خطأ في تسجيل الدخول إلى Instagram:
- تأكد من صحة اسم المستخدم وكلمة المرور
- تأكد من عدم تفعيل المصادقة الثنائية
- جرب تسجيل الدخول من نفس IP أولاً

### خطأ في Telegram Bot:
- تأكد من صحة رمز البوت
- تأكد من إضافة البوت كمشرف في القناة
- تأكد من صحة معرف القناة

### خطأ في Gemini API:
- تأكد من صحة مفتاح API
- تحقق من حدود الاستخدام
- تأكد من تفعيل API في Google Cloud Console

### مشاكل الخطوط:
- تأكد من وجود ملفات الخطوط في مجلد `fonts/`
- استخدم خطوط تدعم العربية
- تحقق من صيغة الملفات (.ttf أو .otf)

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من السجلات في Render
2. تأكد من صحة متغيرات البيئة
3. اختبر كل منصة على حدة
4. راجع ملف README.md للمزيد من التفاصيل
