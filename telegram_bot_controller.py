#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوت Telegram للتحكم الكامل في روبوت نشر الآيات القرآنية
"""

import os
import sys
import asyncio
import logging
import threading
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional
import json

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes

# إعداد الترميز للعربية
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# استيراد الوحدات المخصصة
from config import get_config
from modules.quran_manager import QuranManager
from modules.image_generator import ImageGenerator
from modules.config_manager import ConfigManager

try:
    from modules.gemini_client import AdvancedGeminiClient
    GEMINI_AVAILABLE = True
except:
    GEMINI_AVAILABLE = False

try:
    from modules.telegram_publisher import TelegramPublisher
    TELEGRAM_AVAILABLE = True
except:
    TELEGRAM_AVAILABLE = False

try:
    from modules.instagram_publisher import InstagramPublisher
    INSTAGRAM_AVAILABLE = True
except:
    INSTAGRAM_AVAILABLE = False

try:
    from modules.facebook_publisher import FacebookPublisher
    FACEBOOK_AVAILABLE = True
except:
    FACEBOOK_AVAILABLE = False

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_controller.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TelegramBotController:
    def __init__(self):
        self.config = get_config()
        self.config_manager = ConfigManager()
        self.bot_token = self.config.TELEGRAM_BOT_TOKEN
        self.authorized_users = self._load_authorized_users()
        self.app = None
        
        # تهيئة المكونات
        self.quran_manager = None
        self.image_generator = None
        self.gemini_client = None
        self.telegram_publisher = None
        self.instagram_publisher = None
        self.facebook_publisher = None

        # إعدادات المنصات
        self.platform_settings = {
            'telegram': {'enabled': False, 'configured': False},
            'instagram': {'enabled': False, 'configured': False},
            'facebook': {'enabled': False, 'configured': False}
        }
        
        # إعدادات النشر التلقائي
        self.auto_publish_enabled = False
        self.publish_interval = 10800  # 3 ساعات بالثواني
        self.last_publish_time = None
        self.scheduler_thread = None
        self.scheduler_running = False
        self.state_file = 'data/bot_scheduler_state.json'

        # تحميل حالة الجدولة
        self._load_scheduler_state()

        self._initialize_components()

        # بدء الجدولة التلقائية
        self._start_auto_scheduler()
    
    def _load_authorized_users(self) -> List[int]:
        """تحميل قائمة المستخدمين المصرح لهم"""
        try:
            if os.path.exists('authorized_users.json'):
                with open('authorized_users.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # إنشاء ملف المستخدمين المصرح لهم
                default_users = []
                self._save_authorized_users(default_users)
                return default_users
        except Exception as e:
            logger.error(f"خطأ في تحميل المستخدمين المصرح لهم: {e}")
            return []
    
    def _save_authorized_users(self, users: List[int]):
        """حفظ قائمة المستخدمين المصرح لهم"""
        try:
            with open('authorized_users.json', 'w', encoding='utf-8') as f:
                json.dump(users, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ المستخدمين المصرح لهم: {e}")
    
    def _initialize_components(self):
        """تهيئة المكونات الأساسية"""
        try:
            # المكونات الأساسية
            self.quran_manager = QuranManager()
            logger.info("تم تهيئة مدير القرآن بنجاح")

            self.image_generator = ImageGenerator()
            logger.info("تم تهيئة مولد الصور بنجاح")

            # Gemini AI
            if GEMINI_AVAILABLE and self.config.GEMINI_API_KEY:
                try:
                    self.gemini_client = AdvancedGeminiClient()
                    logger.info("تم تهيئة عميل Gemini بنجاح")
                except Exception as e:
                    logger.warning(f"فشل في تهيئة Gemini: {e}")

            # تهيئة ناشري المنصات
            self._initialize_publishers()

            logger.info("تم تهيئة جميع المكونات بنجاح")

        except Exception as e:
            logger.error(f"خطأ في تهيئة المكونات: {e}")

    def _initialize_publishers(self):
        """تهيئة ناشري المنصات"""
        # Telegram
        if TELEGRAM_AVAILABLE and self.config.ENABLE_TELEGRAM and self.config.TELEGRAM_BOT_TOKEN and self.config.TELEGRAM_CHANNEL_ID:
            try:
                self.telegram_publisher = TelegramPublisher()
                self.platform_settings['telegram']['enabled'] = True
                self.platform_settings['telegram']['configured'] = True
                logger.info("تم تهيئة ناشر Telegram بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة Telegram: {e}")
                self.platform_settings['telegram']['configured'] = False

        # Instagram
        if INSTAGRAM_AVAILABLE and self.config.ENABLE_INSTAGRAM and self.config.INSTAGRAM_USERNAME and self.config.INSTAGRAM_PASSWORD:
            try:
                self.instagram_publisher = InstagramPublisher()
                self.platform_settings['instagram']['enabled'] = True
                self.platform_settings['instagram']['configured'] = True
                logger.info("تم تهيئة ناشر Instagram بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة Instagram: {e}")
                self.platform_settings['instagram']['configured'] = False

        # Facebook
        if FACEBOOK_AVAILABLE and self.config.ENABLE_FACEBOOK and self.config.FACEBOOK_ACCESS_TOKEN and self.config.FACEBOOK_PAGE_ID:
            try:
                self.facebook_publisher = FacebookPublisher()
                self.platform_settings['facebook']['enabled'] = True
                self.platform_settings['facebook']['configured'] = True
                logger.info("تم تهيئة ناشر Facebook بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة Facebook: {e}")
                self.platform_settings['facebook']['configured'] = False
    
    def _load_scheduler_state(self):
        """تحميل حالة الجدولة من الملف"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)

                self.auto_publish_enabled = state.get('auto_publish_enabled', False)
                self.publish_interval = state.get('publish_interval', 10800)  # 3 ساعات افتراضي

                if state.get('last_publish_time'):
                    self.last_publish_time = datetime.fromisoformat(state['last_publish_time'])

                logger.info(f"تم تحميل حالة الجدولة: مفعل={self.auto_publish_enabled}, الفترة={self.publish_interval//3600}س")
        except Exception as e:
            logger.warning(f"فشل في تحميل حالة الجدولة: {e}")

    def _save_scheduler_state(self):
        """حفظ حالة الجدولة في الملف"""
        try:
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)

            state = {
                'auto_publish_enabled': self.auto_publish_enabled,
                'publish_interval': self.publish_interval,
                'last_publish_time': self.last_publish_time.isoformat() if self.last_publish_time else None
            }

            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.warning(f"فشل في حفظ حالة الجدولة: {e}")

    def _start_auto_scheduler(self):
        """بدء نظام الجدولة التلقائية"""
        if not self.scheduler_running:
            self.scheduler_running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            logger.info("تم بدء نظام الجدولة التلقائية")

    def _scheduler_loop(self):
        """حلقة الجدولة التلقائية"""
        while self.scheduler_running:
            try:
                if self.auto_publish_enabled and self._should_publish():
                    logger.info("بدء النشر التلقائي...")
                    asyncio.run(self._auto_publish_content())

                # فحص كل دقيقة
                time.sleep(60)

            except Exception as e:
                logger.error(f"خطأ في حلقة الجدولة: {e}")
                time.sleep(60)

    def _should_publish(self) -> bool:
        """فحص ما إذا كان يجب النشر الآن"""
        if not self.auto_publish_enabled:
            return False

        if self.last_publish_time is None:
            return True

        time_since_last = datetime.now() - self.last_publish_time
        return time_since_last.total_seconds() >= self.publish_interval

    def _get_time_until_next_publish(self) -> int:
        """الحصول على الوقت المتبقي للنشر التالي بالثواني"""
        if not self.auto_publish_enabled or self.last_publish_time is None:
            return 0

        next_publish_time = self.last_publish_time + timedelta(seconds=self.publish_interval)
        time_until_next = next_publish_time - datetime.now()

        return max(0, int(time_until_next.total_seconds()))

    async def _auto_publish_content(self):
        """تنفيذ النشر التلقائي"""
        try:
            logger.info("بدء عملية النشر التلقائي...")

            # الحصول على آية عشوائية
            verse = self.quran_manager.get_random_verse()
            if not verse:
                logger.error("لم يتم العثور على آية للنشر")
                return False

            # الحصول على عبارة إلهامية
            if self.gemini_client:
                try:
                    phrase = self.gemini_client.get_enhanced_phrase()
                except Exception as e:
                    logger.warning(f"فشل في الحصول على عبارة من Gemini: {e}")
                    phrase = self.quran_manager.get_random_inspirational_phrase()
            else:
                phrase = self.quran_manager.get_random_inspirational_phrase()

            # توليد الصورة
            logger.info("بدء توليد الصورة...")
            image_path = self.image_generator.generate_verse_image(verse, phrase)

            if not image_path:
                logger.error("فشل في توليد الصورة - لم يتم إرجاع مسار")
                return False

            if not os.path.exists(image_path):
                logger.error(f"فشل في توليد الصورة - الملف غير موجود: {image_path}")
                return False

            # التحقق من جودة الصورة
            try:
                file_size = os.path.getsize(image_path)
                if file_size < 1024:  # أقل من 1 كيلوبايت
                    logger.error(f"حجم الصورة صغير جداً: {file_size} بايت")
                    return False
                elif file_size > 10 * 1024 * 1024:  # أكبر من 10 ميجابايت
                    logger.warning(f"حجم الصورة كبير: {file_size} بايت")
                else:
                    logger.info(f"✅ تم توليد الصورة بنجاح: {image_path} ({file_size} بايت)")
            except Exception as e:
                logger.warning(f"لا يمكن فحص حجم الصورة: {e}")

            # تحضير المحتوى
            content = {
                'verse': verse,
                'phrase': phrase,
                'caption': f"📖 {verse['surah']} - آية {verse['verse_number']}\n\n💭 {phrase}"
            }

            # النشر على جميع المنصات المفعلة
            success_platforms = []
            failed_platforms = []
            total_platforms = 0

            # Telegram
            if self.telegram_publisher and self.platform_settings['telegram']['enabled']:
                total_platforms += 1
                try:
                    logger.info("محاولة النشر على Telegram...")
                    if self.telegram_publisher.publish_verse_content(image_path, content):
                        success_platforms.append('Telegram')
                        logger.info("✅ تم النشر بنجاح على Telegram")
                    else:
                        failed_platforms.append('Telegram')
                        logger.error("❌ فشل النشر على Telegram")
                except Exception as e:
                    failed_platforms.append('Telegram')
                    logger.error(f"❌ خطأ في النشر على Telegram: {e}")

            # Instagram
            if self.instagram_publisher and self.platform_settings['instagram']['enabled']:
                total_platforms += 1
                try:
                    logger.info("محاولة النشر على Instagram...")
                    if self.instagram_publisher.publish_verse_content(image_path, content):
                        success_platforms.append('Instagram')
                        logger.info("✅ تم النشر بنجاح على Instagram")
                    else:
                        failed_platforms.append('Instagram')
                        logger.error("❌ فشل النشر على Instagram")
                except Exception as e:
                    failed_platforms.append('Instagram')
                    logger.error(f"❌ خطأ في النشر على Instagram: {e}")

            # Facebook
            if self.facebook_publisher and self.platform_settings['facebook']['enabled']:
                total_platforms += 1
                try:
                    logger.info("محاولة النشر على Facebook...")
                    if self.facebook_publisher.publish_verse_content(image_path, content):
                        success_platforms.append('Facebook')
                        logger.info("✅ تم النشر بنجاح على Facebook")
                    else:
                        failed_platforms.append('Facebook')
                        logger.error("❌ فشل النشر على Facebook")
                except Exception as e:
                    failed_platforms.append('Facebook')
                    logger.error(f"❌ خطأ في النشر على Facebook: {e}")

            # تنظيف الصورة المؤقتة
            try:
                if os.path.exists(image_path):
                    os.remove(image_path)
            except Exception as e:
                logger.warning(f"فشل في حذف الصورة المؤقتة: {e}")

            # تحديث وقت آخر نشر إذا نجح على منصة واحدة على الأقل
            if success_platforms:
                self.last_publish_time = datetime.now()
                self._save_scheduler_state()

                success_msg = f"✅ تم النشر التلقائي بنجاح على {len(success_platforms)}/{total_platforms} منصات: {', '.join(success_platforms)}"
                if failed_platforms:
                    success_msg += f"\n❌ فشل على: {', '.join(failed_platforms)}"

                logger.info(success_msg)
                return True
            else:
                error_msg = f"❌ فشل النشر التلقائي على جميع المنصات ({total_platforms} منصات)"
                if failed_platforms:
                    error_msg += f": {', '.join(failed_platforms)}"

                logger.error(error_msg)

                # إضافة نصائح لحل المشاكل
                logger.info("💡 نصائح لحل المشاكل:")
                logger.info("  • استخدم /test_platforms لاختبار الاتصال")
                logger.info("  • استخدم /diagnose_platforms للتشخيص المفصل")
                logger.info("  • تحقق من إعدادات المنصات في ملف .env")

                return False

        except Exception as e:
            logger.error(f"خطأ في النشر التلقائي: {e}")
            return False

    def is_authorized(self, user_id: int) -> bool:
        """التحقق من صلاحية المستخدم"""
        return len(self.authorized_users) == 0 or user_id in self.authorized_users

    async def safe_answer_callback_query(self, query, text: str = None):
        """إجابة آمنة على استعلام الزر مع معالجة الأخطاء"""
        try:
            await query.answer(text)
        except Exception as e:
            logger.warning(f"فشل في الإجابة على الاستعلام: {e}")
            # المتابعة بدون إجابة إذا فشل
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر البدء"""
        user_id = update.effective_user.id
        
        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return
        
        welcome_text = """
🕌 مرحباً بك في بوت التحكم في روبوت نشر الآيات القرآنية

🎯 المميزات المتاحة:
• نشر فوري للآيات القرآنية
• تحكم في النشر التلقائي
• إدارة الإعدادات
• مراقبة الحالة
• إحصائيات مفصلة

📋 الأوامر المتاحة:
/start - عرض هذه الرسالة
/status - حالة النظام
/publish - نشر فوري
/settings - الإعدادات
/stats - الإحصائيات
/help - المساعدة

استخدم الأزرار أدناه للتحكم السريع:
        """
        
        keyboard = [
            [
                InlineKeyboardButton("📊 الحالة", callback_data="status"),
                InlineKeyboardButton("⚡ نشر فوري", callback_data="publish_now")
            ],
            [
                InlineKeyboardButton("📱 إدارة المنصات", callback_data="platforms"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")
            ],
            [
                InlineKeyboardButton("📈 الإحصائيات", callback_data="statistics"),
                InlineKeyboardButton("🔄 تشغيل/إيقاف التلقائي", callback_data="toggle_auto")
            ],
            [
                InlineKeyboardButton("❓ المساعدة", callback_data="help")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(welcome_text, reply_markup=reply_markup)
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض حالة النظام"""
        user_id = update.effective_user.id
        
        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return
        
        status_text = "📊 حالة النظام:\n\n"
        
        # حالة المكونات
        status_text += "🔧 المكونات الأساسية:\n"
        status_text += f"• مدير القرآن: {'✅ يعمل' if self.quran_manager else '❌ معطل'}\n"
        status_text += f"• مولد الصور: {'✅ يعمل' if self.image_generator else '❌ معطل'}\n"
        status_text += f"• Gemini AI: {'✅ يعمل' if self.gemini_client else '❌ معطل'}\n\n"

        # حالة المنصات
        status_text += "📱 المنصات:\n"
        for platform, settings in self.platform_settings.items():
            platform_name = {
                'telegram': 'Telegram',
                'instagram': 'Instagram',
                'facebook': 'Facebook'
            }.get(platform, platform)

            if settings['configured']:
                status_icon = '✅ مفعل' if settings['enabled'] else '⏸️ متوقف'
            else:
                status_icon = '❌ غير مُعد'

            status_text += f"• {platform_name}: {status_icon}\n"

        status_text += "\n"
        
        # حالة النشر التلقائي
        status_text += "🤖 النشر التلقائي:\n"
        status_text += f"• الحالة: {'✅ مفعل' if self.auto_publish_enabled else '❌ معطل'}\n"
        status_text += f"• الفترة: {self.publish_interval // 3600} ساعة\n"
        
        if self.last_publish_time:
            time_diff = datetime.now() - self.last_publish_time
            status_text += f"• آخر نشر: منذ {time_diff.seconds // 60} دقيقة\n"
        else:
            status_text += "• آخر نشر: لم يتم النشر بعد\n"
        
        # التوقيت التالي
        if self.auto_publish_enabled and self.last_publish_time:
            next_publish = self.last_publish_time + timedelta(seconds=self.publish_interval)
            time_until_next = next_publish - datetime.now()
            if time_until_next.total_seconds() > 0:
                hours = int(time_until_next.total_seconds() // 3600)
                minutes = int((time_until_next.total_seconds() % 3600) // 60)
                status_text += f"• النشر التالي: خلال {hours}س {minutes}د\n"
            else:
                status_text += "• النشر التالي: قريباً\n"
        
        keyboard = [
            [
                InlineKeyboardButton("🔄 تحديث", callback_data="status"),
                InlineKeyboardButton("⚡ نشر فوري", callback_data="publish_now")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(status_text, reply_markup=reply_markup)
    
    async def publish_now(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """نشر فوري"""
        user_id = update.effective_user.id
        
        if not self.is_authorized(user_id):
            if update.callback_query:
                await update.callback_query.answer("❌ غير مصرح لك باستخدام هذا البوت")
            else:
                await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return
        
        # إرسال رسالة انتظار
        if update.callback_query:
            await update.callback_query.answer("جاري النشر...")
            message = await update.callback_query.message.reply_text("⏳ جاري إعداد المحتوى...")
        else:
            message = await update.message.reply_text("⏳ جاري إعداد المحتوى...")
        
        try:
            # الحصول على آية عشوائية
            verse = self.quran_manager.get_random_verse()
            if not verse:
                await message.edit_text("❌ لم يتم العثور على آية للنشر")
                return
            
            await message.edit_text("📝 تم اختيار الآية... جاري توليد العبارة الإلهامية...")
            
            # الحصول على عبارة إلهامية
            if self.gemini_client:
                try:
                    phrase = self.gemini_client.get_enhanced_phrase()
                except:
                    phrase = self.quran_manager.get_random_inspirational_phrase()
            else:
                phrase = self.quran_manager.get_random_inspirational_phrase()
            
            await message.edit_text("🎨 جاري إنشاء الصورة...")
            
            # توليد الصورة
            image_path = self.image_generator.generate_verse_image(verse, phrase)
            if not image_path or not os.path.exists(image_path):
                await message.edit_text("❌ فشل في توليد الصورة")
                return
            
            await message.edit_text("📤 جاري النشر على المنصات...")

            # النشر على جميع المنصات المفعلة
            success_platforms = []
            failed_platforms = []

            content = {
                'verse': verse,
                'phrase': phrase,
                'caption': f"📖 {verse['surah']} - آية {verse['verse_number']}\n\n💭 {phrase}"
            }

            # النشر على Telegram
            if self.telegram_publisher and self.platform_settings['telegram']['enabled']:
                try:
                    if self.telegram_publisher.publish_verse_content(image_path, content):
                        success_platforms.append('Telegram')
                    else:
                        failed_platforms.append('Telegram')
                except Exception as e:
                    logger.error(f"خطأ في النشر على Telegram: {e}")
                    failed_platforms.append('Telegram')

            # النشر على Instagram
            if self.instagram_publisher and self.platform_settings['instagram']['enabled']:
                try:
                    if self.instagram_publisher.publish_verse_content(image_path, content):
                        success_platforms.append('Instagram')
                    else:
                        failed_platforms.append('Instagram')
                except Exception as e:
                    logger.error(f"خطأ في النشر على Instagram: {e}")
                    failed_platforms.append('Instagram')

            # النشر على Facebook
            if self.facebook_publisher and self.platform_settings['facebook']['enabled']:
                try:
                    if self.facebook_publisher.publish_verse_content(image_path, content):
                        success_platforms.append('Facebook')
                    else:
                        failed_platforms.append('Facebook')
                except Exception as e:
                    logger.error(f"خطأ في النشر على Facebook: {e}")
                    failed_platforms.append('Facebook')

            # تحديث وقت آخر نشر إذا نجح على منصة واحدة على الأقل
            if success_platforms:
                self.last_publish_time = datetime.now()
            
            # تنظيف الصورة المؤقتة
            try:
                if os.path.exists(image_path):
                    os.remove(image_path)
            except:
                pass
            
            # عرض النتيجة
            if success_platforms:
                result_text = f"""
✅ تم النشر بنجاح!

📖 السورة: {verse['surah']}
🔢 رقم الآية: {verse['verse_number']}
💭 العبارة: {phrase}
⏰ الوقت: {datetime.now().strftime('%H:%M:%S')}

📱 المنصات الناجحة: {', '.join(success_platforms)}
"""
                if failed_platforms:
                    result_text += f"⚠️ المنصات الفاشلة: {', '.join(failed_platforms)}"

                await message.edit_text(result_text)
            else:
                failed_text = "❌ فشل في النشر على جميع المنصات"
                if failed_platforms:
                    failed_text += f"\n\nالمنصات المحاولة: {', '.join(failed_platforms)}"
                await message.edit_text(failed_text)
                
        except Exception as e:
            logger.error(f"خطأ في النشر الفوري: {e}")
            await message.edit_text(f"❌ خطأ في النشر: {str(e)}")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الأزرار"""
        query = update.callback_query

        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await self.safe_answer_callback_query(query, "❌ غير مصرح لك باستخدام هذا البوت")
            return

        # الإجابة على الاستعلام مع معالجة الأخطاء
        await self.safe_answer_callback_query(query)
        
        if query.data == "status":
            await self.status_command(update, context)
        elif query.data == "publish_now":
            await self.publish_now(update, context)
        elif query.data == "toggle_auto":
            await self.toggle_auto_publish(update, context)
        elif query.data == "settings":
            await self.show_settings(update, context)
        elif query.data == "statistics":
            await self.show_statistics(update, context)
        elif query.data == "help":
            await self.show_help(update, context)
        elif query.data == "platforms":
            await self.show_platforms(update, context)
        elif query.data.startswith("toggle_platform_"):
            platform = query.data.replace("toggle_platform_", "")
            await self.toggle_platform(update, context, platform)
        elif query.data == "configure_platforms":
            await self.show_platform_config(update, context)
        elif query.data.startswith("edit_"):
            platform = query.data.replace("edit_", "")
            await self.edit_platform_config(update, context, platform)
        elif query.data == "reload_config":
            await self.reload_configuration(update, context)
        elif query.data == "config_summary":
            await self.show_config_summary(update, context)
        elif query.data == "config_history":
            await self.show_config_history(update, context)
        elif query.data.startswith("save_channel_"):
            channel_id = query.data.replace("save_channel_", "")
            await self.save_channel_from_button(update, context, channel_id)
        elif query.data == "ignore_channel":
            await self.safe_answer_callback_query(query, "تم تجاهل معرف القناة")
            await query.message.delete()
        elif query.data == "update_channel_id":
            await self.update_channel_id_automatically(update, context)
        elif query.data == "toggle_auto_publish":
            await self.toggle_auto_publish(update, context)
        elif query.data == "show_status":
            await self.status_command(update, context)
        elif query.data == "show_settings":
            await self.show_settings(update, context)
        elif query.data.startswith("set_interval_"):
            hours = int(query.data.replace("set_interval_", ""))
            await self.set_interval_from_button(update, context, hours)
        elif query.data == "show_main_menu":
            await self.start_command(update, context)
    
    async def toggle_auto_publish(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تشغيل/إيقاف النشر التلقائي"""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        self.auto_publish_enabled = not self.auto_publish_enabled
        self._save_scheduler_state()  # حفظ الحالة

        status = "مفعل" if self.auto_publish_enabled else "معطل"
        interval_hours = self.publish_interval // 3600

        if self.auto_publish_enabled:
            next_publish_seconds = self._get_time_until_next_publish()
            if next_publish_seconds > 0:
                hours = next_publish_seconds // 3600
                minutes = (next_publish_seconds % 3600) // 60
                next_publish_text = f"\n⏰ النشر التالي خلال: {hours}س {minutes}د"
            else:
                next_publish_text = "\n⏰ النشر التالي: قريباً"
        else:
            next_publish_text = ""

        text = f"""🔄 تم {'تفعيل' if self.auto_publish_enabled else 'تعطيل'} النشر التلقائي

📊 الحالة الحالية: {status}
⏱ فترة النشر: كل {interval_hours} ساعات{next_publish_text}

💡 يمكنك تغيير فترة النشر باستخدام /set_interval"""

        if update.callback_query:
            await update.callback_query.message.reply_text(text)
        else:
            await update.message.reply_text(text)
    
    async def show_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض الإعدادات"""
        settings_text = f"""
⚙️ إعدادات النظام:

🤖 النشر التلقائي: {'✅ مفعل' if self.auto_publish_enabled else '❌ معطل'}
⏰ فترة النشر: {self.publish_interval // 3600} ساعة
📱 قناة النشر: {self.config.TELEGRAM_CHANNEL_ID}

🔧 المكونات المفعلة:
• Gemini AI: {'✅' if self.gemini_client else '❌'}
• Telegram: {'✅' if self.telegram_publisher else '❌'}

للتعديل، استخدم الأوامر التالية:
/set_interval <ساعات> - تغيير فترة النشر
/toggle_auto - تشغيل/إيقاف التلقائي
        """
        
        keyboard = [
            [
                InlineKeyboardButton("🔄 تشغيل/إيقاف", callback_data="toggle_auto"),
                InlineKeyboardButton("📊 الحالة", callback_data="status")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if update.callback_query:
            await update.callback_query.message.reply_text(settings_text, reply_markup=reply_markup)
        else:
            await update.message.reply_text(settings_text, reply_markup=reply_markup)
    
    async def show_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض الإحصائيات"""
        stats_text = f"""
📈 إحصائيات النظام:

⏰ آخر نشر: {self.last_publish_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_publish_time else 'لم يتم النشر بعد'}
🤖 النشر التلقائي: {'مفعل' if self.auto_publish_enabled else 'معطل'}
⏱️ فترة النشر: {self.publish_interval // 3600} ساعة

📊 إحصائيات القرآن:
• إجمالي الآيات: {len(self.quran_manager.verses_data['verses']) if self.quran_manager else 0}
• الآيات المستخدمة: {len(self.quran_manager.used_verses) if self.quran_manager else 0}

🔧 حالة المكونات:
• مدير القرآن: {'✅' if self.quran_manager else '❌'}
• مولد الصور: {'✅' if self.image_generator else '❌'}
• Gemini AI: {'✅' if self.gemini_client else '❌'}
• ناشر Telegram: {'✅' if self.telegram_publisher else '❌'}
        """
        
        if update.callback_query:
            await update.callback_query.message.reply_text(stats_text)
        else:
            await update.message.reply_text(stats_text)
    
    async def show_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض المساعدة"""
        help_text = """
❓ مساعدة بوت التحكم:

📋 الأوامر الأساسية:
/start - بدء البوت وعرض القائمة الرئيسية
/status - عرض حالة النظام والمكونات
/publish - نشر آية قرآنية فوراً
/settings - عرض وتعديل الإعدادات
/stats - عرض الإحصائيات المفصلة
/help - عرض هذه المساعدة
/platforms - إدارة المنصات

⚙️ أوامر النشر التلقائي:
/toggle_auto - تشغيل/إيقاف النشر التلقائي
/set_interval <ساعات> - تغيير فترة النشر التلقائي
/auto_status - عرض حالة النشر التلقائي المفصلة
/force_publish - نشر فوري مع تحديث وقت آخر نشر
/quick_setup - إعداد سريع لفترة النشر
/test_platforms - اختبار اتصال جميع المنصات
/diagnose_platforms - تشخيص مفصل لمشاكل المنصات

📱 أوامر إعدادات Telegram:
/set_enable_telegram true/false - تفعيل/تعطيل Telegram
/set_telegram_bot_token <token> - تعديل رمز البوت
/set_telegram_channel_id <id> - تعديل معرف القناة

📸 أوامر إعدادات Instagram:
/set_enable_instagram true/false - تفعيل/تعطيل Instagram
/set_instagram_username <username> - تعديل اسم المستخدم
/set_instagram_password <password> - تعديل كلمة المرور

📘 أوامر إعدادات Facebook:
/set_enable_facebook true/false - تفعيل/تعطيل Facebook
/set_facebook_access_token <token> - تعديل رمز الوصول
/set_facebook_page_id <id> - تعديل معرف الصفحة

🤖 أوامر إعدادات Gemini:
/set_gemini_api_key <key> - تعديل مفتاح API

💡 نصائح:
• استخدم الأزرار للتحكم السريع
• يمكنك تغيير فترة النشر من 1-24 ساعة
• النظام ينشر تلقائياً حسب الفترة المحددة
• جميع العمليات مسجلة في السجلات
• بعد تعديل الإعدادات، استخدم زر "إعادة تحميل الإعدادات"

🆘 في حالة المشاكل:
• تحقق من حالة النظام باستخدام /status
• راجع الإعدادات باستخدام /platforms
• تأكد من صحة رموز API في ملف .env
        """

        if update.callback_query:
            await update.callback_query.message.reply_text(help_text)
        else:
            await update.message.reply_text(help_text)

    async def show_platforms(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة المنصات"""
        platforms_text = "📱 إدارة المنصات:\n\n"

        for platform, settings in self.platform_settings.items():
            platform_name = {
                'telegram': 'Telegram',
                'instagram': 'Instagram',
                'facebook': 'Facebook'
            }.get(platform, platform)

            if settings['configured']:
                status = '✅ مفعل' if settings['enabled'] else '⏸️ متوقف'
            else:
                status = '❌ غير مُعد'

            platforms_text += f"• {platform_name}: {status}\n"

        platforms_text += "\nاستخدم الأزرار أدناه للتحكم:"

        keyboard = []
        for platform, settings in self.platform_settings.items():
            platform_name = {
                'telegram': 'Telegram',
                'instagram': 'Instagram',
                'facebook': 'Facebook'
            }.get(platform, platform)

            if settings['configured']:
                button_text = f"{'⏸️' if settings['enabled'] else '▶️'} {platform_name}"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=f"toggle_platform_{platform}")])

        keyboard.append([
            InlineKeyboardButton("⚙️ إعداد المنصات", callback_data="configure_platforms"),
            InlineKeyboardButton("🔄 تحديث", callback_data="platforms")
        ])

        reply_markup = InlineKeyboardMarkup(keyboard)

        if update.callback_query:
            await update.callback_query.message.reply_text(platforms_text, reply_markup=reply_markup)
        else:
            await update.message.reply_text(platforms_text, reply_markup=reply_markup)

    async def toggle_platform(self, update: Update, context: ContextTypes.DEFAULT_TYPE, platform: str):
        """تشغيل/إيقاف منصة"""
        if platform in self.platform_settings and self.platform_settings[platform]['configured']:
            self.platform_settings[platform]['enabled'] = not self.platform_settings[platform]['enabled']

            platform_name = {
                'telegram': 'Telegram',
                'instagram': 'Instagram',
                'facebook': 'Facebook'
            }.get(platform, platform)

            status = 'تم تفعيل' if self.platform_settings[platform]['enabled'] else 'تم إيقاف'

            await self.safe_answer_callback_query(update.callback_query, f"{status} {platform_name}")

            await self.show_platforms(update, context)
        else:
            await self.safe_answer_callback_query(update.callback_query, "المنصة غير مُعدة بشكل صحيح")

    async def show_platform_config(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إعدادات المنصات الحقيقية"""
        config_text = "⚙️ الإعدادات الحالية:\n\n"

        # عرض إعدادات Telegram
        config_text += "📱 Telegram:\n"
        config_text += f"• مفعل: {'✅ نعم' if self.config.ENABLE_TELEGRAM else '❌ لا'}\n"
        if self.config.TELEGRAM_BOT_TOKEN:
            masked_token = self.config.TELEGRAM_BOT_TOKEN[:10] + "..." + self.config.TELEGRAM_BOT_TOKEN[-5:]
            config_text += f"• رمز البوت: {masked_token}\n"
        else:
            config_text += "• رمز البوت: ❌ غير محدد\n"
        # عرض معرف القناة مع إمكانية التحديث
        if self.config.TELEGRAM_CHANNEL_ID:
            channel_id = self.config.TELEGRAM_CHANNEL_ID
            if not channel_id.lstrip('-').isdigit():
                config_text += f"• معرف القناة: {channel_id} ⚠️ (نصي - يُنصح بالحصول على المعرف الرقمي)\n"
            else:
                config_text += f"• معرف القناة: {channel_id} ✅ (رقمي)\n"
        else:
            config_text += "• معرف القناة: ❌ غير محدد\n"
        config_text += "\n"

        # عرض إعدادات Instagram
        config_text += "📸 Instagram:\n"
        config_text += f"• مفعل: {'✅ نعم' if self.config.ENABLE_INSTAGRAM else '❌ لا'}\n"
        config_text += f"• اسم المستخدم: {self.config.INSTAGRAM_USERNAME or '❌ غير محدد'}\n"
        if self.config.INSTAGRAM_PASSWORD:
            config_text += f"• كلمة المرور: {'*' * len(self.config.INSTAGRAM_PASSWORD)}\n"
        else:
            config_text += "• كلمة المرور: ❌ غير محددة\n\n"

        # عرض إعدادات Facebook
        config_text += "📘 Facebook:\n"
        config_text += f"• مفعل: {'✅ نعم' if self.config.ENABLE_FACEBOOK else '❌ لا'}\n"
        if self.config.FACEBOOK_ACCESS_TOKEN:
            masked_token = self.config.FACEBOOK_ACCESS_TOKEN[:15] + "..." + self.config.FACEBOOK_ACCESS_TOKEN[-5:]
            config_text += f"• رمز الوصول: {masked_token}\n"
        else:
            config_text += "• رمز الوصول: ❌ غير محدد\n"
        config_text += f"• معرف الصفحة: {self.config.FACEBOOK_PAGE_ID or '❌ غير محدد'}\n\n"

        # عرض إعدادات Gemini
        config_text += "🤖 Gemini AI:\n"
        if self.config.GEMINI_API_KEY:
            masked_key = self.config.GEMINI_API_KEY[:10] + "..." + self.config.GEMINI_API_KEY[-5:]
            config_text += f"• مفتاح API: {masked_key}\n\n"
        else:
            config_text += "• مفتاح API: ❌ غير محدد\n\n"

        config_text += "💡 لتعديل الإعدادات، استخدم الأزرار أدناه:"

        keyboard = [
            [
                InlineKeyboardButton("📱 تعديل Telegram", callback_data="edit_telegram"),
                InlineKeyboardButton("📸 تعديل Instagram", callback_data="edit_instagram")
            ],
            [
                InlineKeyboardButton("📘 تعديل Facebook", callback_data="edit_facebook"),
                InlineKeyboardButton("🤖 تعديل Gemini", callback_data="edit_gemini")
            ],
            [
                InlineKeyboardButton("📋 ملخص الإعدادات", callback_data="config_summary"),
                InlineKeyboardButton("📜 تاريخ التغييرات", callback_data="config_history")
            ]
        ]

        # إضافة زر تحديث معرف القناة إذا كان نصياً
        if (self.config.TELEGRAM_CHANNEL_ID and
            not self.config.TELEGRAM_CHANNEL_ID.lstrip('-').isdigit()):
            keyboard.append([
                InlineKeyboardButton("🔄 تحديث معرف القناة تلقائياً", callback_data="update_channel_id")
            ])

        keyboard.append([
            InlineKeyboardButton("🔄 إعادة تحميل الإعدادات", callback_data="reload_config"),
            InlineKeyboardButton("🔙 العودة للمنصات", callback_data="platforms")
        ])

        reply_markup = InlineKeyboardMarkup(keyboard)

        if update.callback_query:
            await update.callback_query.message.reply_text(config_text, reply_markup=reply_markup)
        else:
            await update.message.reply_text(config_text, reply_markup=reply_markup)

    async def set_publish_interval(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تعيين فترة النشر التلقائي"""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد عدد الساعات\nمثال: /set_interval 6")
            return

        try:
            hours = int(context.args[0])
            if hours < 1 or hours > 24:
                await update.message.reply_text("❌ يجب أن تكون الفترة بين 1-24 ساعة")
                return

            old_interval = self.publish_interval // 3600
            self.publish_interval = hours * 3600  # تحويل إلى ثواني
            self._save_scheduler_state()  # حفظ الحالة

            # حساب الوقت للنشر التالي
            if self.auto_publish_enabled:
                next_publish_seconds = self._get_time_until_next_publish()
                if next_publish_seconds > 0:
                    next_hours = next_publish_seconds // 3600
                    next_minutes = (next_publish_seconds % 3600) // 60
                    next_publish_text = f"\n⏰ النشر التالي خلال: {next_hours}س {next_minutes}د"
                else:
                    next_publish_text = "\n⏰ النشر التالي: قريباً"
            else:
                next_publish_text = "\n⚠️ النشر التلقائي معطل حالياً"

            await update.message.reply_text(f"""✅ تم تحديث فترة النشر

📊 الفترة السابقة: {old_interval} ساعة
📊 الفترة الجديدة: {hours} ساعة{next_publish_text}

💡 استخدم /toggle_auto لتفعيل/تعطيل النشر التلقائي""")

        except ValueError:
            await update.message.reply_text("❌ يرجى إدخال رقم صحيح\nمثال: /set_interval 6")

    async def auto_status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض حالة النشر التلقائي"""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        # حساب الإحصائيات
        interval_hours = self.publish_interval // 3600
        status_emoji = "✅" if self.auto_publish_enabled else "❌"
        status_text = "مفعل" if self.auto_publish_enabled else "معطل"

        # حساب الوقت للنشر التالي
        if self.auto_publish_enabled and self.last_publish_time:
            next_publish_seconds = self._get_time_until_next_publish()
            if next_publish_seconds > 0:
                hours = next_publish_seconds // 3600
                minutes = (next_publish_seconds % 3600) // 60
                next_publish_text = f"{hours}س {minutes}د"
            else:
                next_publish_text = "قريباً"
        elif self.auto_publish_enabled:
            next_publish_text = "قريباً (أول نشر)"
        else:
            next_publish_text = "معطل"

        # حساب المنصات المفعلة
        enabled_platforms = []
        for platform, settings in self.platform_settings.items():
            if settings['enabled'] and settings['configured']:
                enabled_platforms.append(platform.title())

        platforms_text = ", ".join(enabled_platforms) if enabled_platforms else "لا توجد منصات مفعلة"

        # آخر نشر
        if self.last_publish_time:
            last_publish_text = self.last_publish_time.strftime("%Y-%m-%d %H:%M")
        else:
            last_publish_text = "لم يتم النشر بعد"

        status_message = f"""🤖 حالة النشر التلقائي

{status_emoji} الحالة: {status_text}
⏱ فترة النشر: كل {interval_hours} ساعات
⏰ النشر التالي: {next_publish_text}
📅 آخر نشر: {last_publish_text}

📱 المنصات المفعلة:
{platforms_text}

🎯 الأوامر المتاحة:
/toggle_auto - تشغيل/إيقاف النشر التلقائي
/set_interval [ساعات] - تغيير فترة النشر
/publish - نشر فوري
/status - حالة النظام العامة"""

        # إضافة أزرار سريعة
        keyboard = [
            [
                InlineKeyboardButton("🔄 تشغيل/إيقاف", callback_data="toggle_auto_publish"),
                InlineKeyboardButton("📊 حالة النظام", callback_data="show_status")
            ],
            [
                InlineKeyboardButton("⚡ نشر فوري", callback_data="publish_now"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data="show_settings")
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(status_message, reply_markup=reply_markup)

    async def force_publish_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """نشر فوري مع تحديث وقت آخر نشر"""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        await update.message.reply_text("⚡ بدء النشر الفوري...")

        try:
            success = await self._auto_publish_content()

            if success:
                # حساب الوقت للنشر التالي
                if self.auto_publish_enabled:
                    next_publish_seconds = self._get_time_until_next_publish()
                    hours = next_publish_seconds // 3600
                    minutes = (next_publish_seconds % 3600) // 60
                    next_text = f"\n⏰ النشر التالي خلال: {hours}س {minutes}د"
                else:
                    next_text = "\n⚠️ النشر التلقائي معطل حالياً"

                await update.message.reply_text(f"✅ تم النشر الفوري بنجاح!{next_text}")
            else:
                await update.message.reply_text("❌ فشل في النشر الفوري. تحقق من إعدادات المنصات.")

        except Exception as e:
            logger.error(f"خطأ في النشر الفوري: {e}")
            await update.message.reply_text(f"❌ حدث خطأ في النشر الفوري: {str(e)}")

    async def quick_setup_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إعداد سريع للنشر التلقائي"""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        setup_text = """🚀 الإعداد السريع للنشر التلقائي

اختر فترة النشر المرغوبة:"""

        keyboard = [
            [
                InlineKeyboardButton("⏰ كل ساعة", callback_data="set_interval_1"),
                InlineKeyboardButton("⏰ كل ساعتين", callback_data="set_interval_2")
            ],
            [
                InlineKeyboardButton("⏰ كل 3 ساعات", callback_data="set_interval_3"),
                InlineKeyboardButton("⏰ كل 4 ساعات", callback_data="set_interval_4")
            ],
            [
                InlineKeyboardButton("⏰ كل 6 ساعات", callback_data="set_interval_6"),
                InlineKeyboardButton("⏰ كل 8 ساعات", callback_data="set_interval_8")
            ],
            [
                InlineKeyboardButton("⏰ كل 12 ساعة", callback_data="set_interval_12"),
                InlineKeyboardButton("⏰ كل 24 ساعة", callback_data="set_interval_24")
            ],
            [
                InlineKeyboardButton("🔙 العودة", callback_data="show_main_menu")
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(setup_text, reply_markup=reply_markup)

    async def set_interval_from_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE, hours: int):
        """تعيين فترة النشر من الزر"""
        old_interval = self.publish_interval // 3600
        self.publish_interval = hours * 3600
        self._save_scheduler_state()

        # حساب الوقت للنشر التالي
        if self.auto_publish_enabled:
            next_publish_seconds = self._get_time_until_next_publish()
            if next_publish_seconds > 0:
                next_hours = next_publish_seconds // 3600
                next_minutes = (next_publish_seconds % 3600) // 60
                next_publish_text = f"\n⏰ النشر التالي خلال: {next_hours}س {next_minutes}د"
            else:
                next_publish_text = "\n⏰ النشر التالي: قريباً"
        else:
            next_publish_text = "\n⚠️ النشر التلقائي معطل حالياً"

        message_text = f"""✅ تم تحديث فترة النشر

📊 الفترة السابقة: {old_interval} ساعة
📊 الفترة الجديدة: {hours} ساعة{next_publish_text}

💡 استخدم /toggle_auto لتفعيل/تعطيل النشر التلقائي"""

        # أزرار سريعة
        keyboard = [
            [
                InlineKeyboardButton("🔄 تشغيل/إيقاف النشر", callback_data="toggle_auto_publish"),
                InlineKeyboardButton("⚡ نشر فوري", callback_data="publish_now")
            ],
            [
                InlineKeyboardButton("📊 حالة النظام", callback_data="show_status"),
                InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="show_main_menu")
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        if update.callback_query:
            await update.callback_query.message.edit_text(message_text, reply_markup=reply_markup)
        else:
            await update.message.reply_text(message_text, reply_markup=reply_markup)

    async def diagnose_platforms_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تشخيص مشاكل المنصات"""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        await update.message.reply_text("🔍 جاري تشخيص مشاكل المنصات...")

        diagnosis_report = "📋 تقرير تشخيص المنصات:\n\n"

        # تشخيص Telegram
        if self.telegram_publisher:
            diagnosis_report += "📱 **Telegram:**\n"
            try:
                telegram_diagnosis = await self.telegram_publisher.diagnose_issues()

                if telegram_diagnosis['bot_token_valid']:
                    diagnosis_report += "✅ رمز البوت صحيح\n"
                else:
                    diagnosis_report += "❌ رمز البوت غير صحيح\n"

                if telegram_diagnosis['channel_accessible']:
                    diagnosis_report += "✅ القناة متاحة\n"
                else:
                    diagnosis_report += "❌ القناة غير متاحة\n"

                if telegram_diagnosis['bot_permissions']:
                    diagnosis_report += "✅ البوت لديه صلاحيات\n"
                else:
                    diagnosis_report += "❌ البوت لا يملك صلاحيات\n"

                if telegram_diagnosis['issues']:
                    diagnosis_report += "🔴 المشاكل:\n"
                    for issue in telegram_diagnosis['issues']:
                        diagnosis_report += f"  • {issue}\n"

                if telegram_diagnosis['recommendations']:
                    diagnosis_report += "💡 التوصيات:\n"
                    for rec in telegram_diagnosis['recommendations']:
                        diagnosis_report += f"  • {rec}\n"

            except Exception as e:
                diagnosis_report += f"❌ خطأ في التشخيص: {e}\n"

            diagnosis_report += "\n"

        # تشخيص Instagram
        if self.instagram_publisher:
            diagnosis_report += "📸 **Instagram:**\n"
            try:
                instagram_diagnosis = self.instagram_publisher.diagnose_issues()

                if instagram_diagnosis['credentials_valid']:
                    diagnosis_report += "✅ بيانات الاعتماد متوفرة\n"
                else:
                    diagnosis_report += "❌ بيانات الاعتماد مفقودة\n"

                if instagram_diagnosis['login_successful']:
                    diagnosis_report += "✅ تسجيل الدخول ناجح\n"
                else:
                    diagnosis_report += "❌ فشل في تسجيل الدخول\n"

                if instagram_diagnosis['account_accessible']:
                    diagnosis_report += "✅ الحساب متاح\n"
                else:
                    diagnosis_report += "❌ الحساب غير متاح\n"

                if instagram_diagnosis['issues']:
                    diagnosis_report += "🔴 المشاكل:\n"
                    for issue in instagram_diagnosis['issues']:
                        diagnosis_report += f"  • {issue}\n"

                if instagram_diagnosis['recommendations']:
                    diagnosis_report += "💡 التوصيات:\n"
                    for rec in instagram_diagnosis['recommendations']:
                        diagnosis_report += f"  • {rec}\n"

            except Exception as e:
                diagnosis_report += f"❌ خطأ في التشخيص: {e}\n"

            diagnosis_report += "\n"

        # تشخيص Facebook
        if self.facebook_publisher:
            diagnosis_report += "📘 **Facebook:**\n"
            diagnosis_report += "⚠️ تشخيص Facebook غير متوفر حالياً\n\n"

        # إضافة نصائح عامة
        diagnosis_report += """🛠 نصائح عامة لحل المشاكل:

1. تأكد من صحة جميع المفاتيح في ملف .env
2. تحقق من اتصال الإنترنت
3. تأكد من أن البوت مضاف كمدير في قناة Telegram
4. تجنب استخدام المصادقة الثنائية في Instagram
5. استخدم /test_platforms لاختبار الاتصال"""

        # تقسيم الرسالة إذا كانت طويلة
        if len(diagnosis_report) > 4000:
            parts = [diagnosis_report[i:i+4000] for i in range(0, len(diagnosis_report), 4000)]
            for i, part in enumerate(parts):
                if i == 0:
                    await update.message.reply_text(part, parse_mode='Markdown')
                else:
                    await update.message.reply_text(f"📋 تقرير التشخيص (الجزء {i+1}):\n\n{part}", parse_mode='Markdown')
        else:
            await update.message.reply_text(diagnosis_report, parse_mode='Markdown')

    async def test_platforms_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """اختبار اتصال المنصات"""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        await update.message.reply_text("🧪 جاري اختبار اتصال المنصات...")

        test_results = "📊 نتائج اختبار المنصات:\n\n"

        # اختبار Telegram
        if self.telegram_publisher:
            test_results += "📱 **Telegram:** "
            try:
                if await self.telegram_publisher.test_connection():
                    test_results += "✅ متصل\n"
                else:
                    test_results += "❌ غير متصل\n"
            except Exception as e:
                test_results += f"❌ خطأ: {e}\n"
        else:
            test_results += "📱 **Telegram:** ❌ غير مُعد\n"

        # اختبار Instagram
        if self.instagram_publisher:
            test_results += "📸 **Instagram:** "
            try:
                if self.instagram_publisher.test_connection():
                    test_results += "✅ متصل\n"
                else:
                    test_results += "❌ غير متصل\n"
            except Exception as e:
                test_results += f"❌ خطأ: {e}\n"
        else:
            test_results += "📸 **Instagram:** ❌ غير مُعد\n"

        # اختبار Facebook
        if self.facebook_publisher:
            test_results += "📘 **Facebook:** "
            try:
                if hasattr(self.facebook_publisher, 'test_connection'):
                    if self.facebook_publisher.test_connection():
                        test_results += "✅ متصل\n"
                    else:
                        test_results += "❌ غير متصل\n"
                else:
                    test_results += "⚠️ اختبار غير متوفر\n"
            except Exception as e:
                test_results += f"❌ خطأ: {e}\n"
        else:
            test_results += "📘 **Facebook:** ❌ غير مُعد\n"

        test_results += "\n💡 استخدم /diagnose_platforms للحصول على تشخيص مفصل"

        await update.message.reply_text(test_results, parse_mode='Markdown')

    async def edit_platform_config(self, update: Update, context: ContextTypes.DEFAULT_TYPE, platform: str):
        """تعديل إعدادات منصة معينة"""
        platform_configs = {
            'telegram': {
                'name': 'Telegram',
                'fields': [
                    ('ENABLE_TELEGRAM', 'تفعيل Telegram', 'boolean'),
                    ('TELEGRAM_BOT_TOKEN', 'رمز البوت', 'text'),
                    ('TELEGRAM_CHANNEL_ID', 'معرف القناة', 'text')
                ]
            },
            'instagram': {
                'name': 'Instagram',
                'fields': [
                    ('ENABLE_INSTAGRAM', 'تفعيل Instagram', 'boolean'),
                    ('INSTAGRAM_USERNAME', 'اسم المستخدم', 'text'),
                    ('INSTAGRAM_PASSWORD', 'كلمة المرور', 'password')
                ]
            },
            'facebook': {
                'name': 'Facebook',
                'fields': [
                    ('ENABLE_FACEBOOK', 'تفعيل Facebook', 'boolean'),
                    ('FACEBOOK_ACCESS_TOKEN', 'رمز الوصول', 'text'),
                    ('FACEBOOK_PAGE_ID', 'معرف الصفحة', 'text')
                ]
            },
            'gemini': {
                'name': 'Gemini AI',
                'fields': [
                    ('GEMINI_API_KEY', 'مفتاح API', 'text')
                ]
            }
        }

        if platform not in platform_configs:
            await update.callback_query.answer("منصة غير صحيحة")
            return

        config = platform_configs[platform]
        config_text = f"⚙️ تعديل إعدادات {config['name']}:\n\n"
        config_text += "القيم الحالية:\n"

        for field_key, field_name, field_type in config['fields']:
            current_value = getattr(self.config, field_key, None)
            if field_type == 'boolean':
                display_value = '✅ مفعل' if current_value else '❌ معطل'
            elif field_type == 'password' and current_value:
                display_value = '*' * len(str(current_value))
            elif current_value:
                if len(str(current_value)) > 20:
                    display_value = str(current_value)[:15] + "..." + str(current_value)[-5:]
                else:
                    display_value = str(current_value)
            else:
                display_value = '❌ غير محدد'

            config_text += f"• {field_name}: {display_value}\n"

        config_text += f"\n💡 لتعديل إعدادات {config['name']}، استخدم الأوامر التالية:\n"
        for field_key, field_name, field_type in config['fields']:
            if field_type == 'boolean':
                config_text += f"/set_{field_key.lower()} true/false\n"
            else:
                config_text += f"/set_{field_key.lower()} <القيمة>\n"

        keyboard = [
            [InlineKeyboardButton("🔙 العودة للإعدادات", callback_data="configure_platforms")]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.callback_query.message.reply_text(config_text, reply_markup=reply_markup)

    async def reload_configuration(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إعادة تحميل الإعدادات"""
        try:
            # إعادة تحميل الإعدادات
            from importlib import reload
            import config
            reload(config)
            self.config = config.get_config()

            # إعادة تهيئة المكونات
            self._initialize_publishers()

            await self.safe_answer_callback_query(update.callback_query, "✅ تم إعادة تحميل الإعدادات بنجاح")

            await self.show_platform_config(update, context)

        except Exception as e:
            logger.error(f"خطأ في إعادة تحميل الإعدادات: {e}")
            await self.safe_answer_callback_query(update.callback_query, "❌ فشل في إعادة تحميل الإعدادات")

            # إرسال رسالة خطأ
            if update.callback_query and update.callback_query.message:
                await update.callback_query.message.reply_text("❌ فشل في إعادة تحميل الإعدادات")

    def _update_env_file(self, key: str, value: str, user_id: Optional[int] = None):
        """تحديث ملف .env باستخدام مدير الإعدادات"""
        return self.config_manager.update_config_value(key, value, user_id)

    # دوال تعديل إعدادات Telegram
    async def set_enable_telegram(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تفعيل/تعطيل Telegram"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد true أو false\nمثال: /set_enable_telegram true")
            return

        value = context.args[0].lower()
        if value not in ['true', 'false']:
            await update.message.reply_text("❌ القيمة يجب أن تكون true أو false")
            return

        if self._update_env_file('ENABLE_TELEGRAM', value, update.effective_user.id):
            await update.message.reply_text(f"✅ تم تعديل تفعيل Telegram إلى: {value}")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")

    async def set_telegram_bot_token(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تعديل رمز بوت Telegram"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد رمز البوت\nمثال: /set_telegram_bot_token 123456:ABC-DEF...")
            return

        token = context.args[0]
        if self._update_env_file('TELEGRAM_BOT_TOKEN', token, update.effective_user.id):
            await update.message.reply_text("✅ تم تحديث رمز بوت Telegram بنجاح")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")

    async def set_telegram_channel_id(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تعديل معرف قناة Telegram مع الحصول التلقائي على الـ ID"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد معرف القناة\nمثال: /set_telegram_channel_id @mychannel")
            return

        channel_input = context.args[0]

        # إرسال رسالة انتظار
        status_message = await update.message.reply_text("⏳ جاري التحقق من القناة والحصول على المعرف...")

        try:
            # محاولة الحصول على معلومات القناة
            channel_id = await self._get_channel_id(channel_input)

            if channel_id:
                # حفظ معرف القناة الرقمي
                if self._update_env_file('TELEGRAM_CHANNEL_ID', str(channel_id), update.effective_user.id):
                    await status_message.edit_text(
                        f"✅ تم تحديث معرف قناة Telegram بنجاح!\n\n"
                        f"📝 المعرف المدخل: {channel_input}\n"
                        f"🆔 المعرف الرقمي: {channel_id}\n\n"
                        f"💡 تم حفظ المعرف الرقمي تلقائياً للاستخدام في النشر"
                    )
                else:
                    await status_message.edit_text("❌ فشل في حفظ الإعدادات")
            else:
                # إذا فشل الحصول على المعرف الرقمي، احفظ المعرف كما هو
                if self._update_env_file('TELEGRAM_CHANNEL_ID', channel_input, update.effective_user.id):
                    await status_message.edit_text(
                        f"⚠️ تم حفظ معرف القناة: {channel_input}\n\n"
                        f"❗ لم يتمكن البوت من الحصول على المعرف الرقمي تلقائياً.\n"
                        f"💡 تأكد من أن البوت مضاف كمشرف في القناة للحصول على المعرف الرقمي."
                    )
                else:
                    await status_message.edit_text("❌ فشل في حفظ الإعدادات")

        except Exception as e:
            logger.error(f"خطأ في تحديث معرف القناة: {e}")
            await status_message.edit_text(f"❌ خطأ في التحقق من القناة: {str(e)}")

    async def _get_channel_id(self, channel_input: str):
        """الحصول على معرف القناة الرقمي"""
        try:
            if not self.app:
                return None

            # إذا كان المعرف رقمياً بالفعل، أرجعه كما هو
            if channel_input.lstrip('-').isdigit():
                return channel_input

            # محاولة الحصول على معلومات القناة
            chat = await self.app.bot.get_chat(channel_input)
            return chat.id

        except Exception as e:
            logger.warning(f"فشل في الحصول على معرف القناة {channel_input}: {e}")
            return None

    async def handle_text_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الرسائل النصية للتعرف على معرفات القنوات"""
        if not self.is_authorized(update.effective_user.id):
            return

        text = update.message.text.strip()

        # التحقق من أن النص يشبه معرف قناة
        if self._is_channel_identifier(text):
            # استخراج اسم المستخدم من الرابط إذا لزم الأمر
            processed_channel = self._extract_channel_username(text)

            keyboard = [
                [
                    InlineKeyboardButton("✅ نعم، احفظ كمعرف قناة", callback_data=f"save_channel_{text}"),
                    InlineKeyboardButton("❌ لا، تجاهل", callback_data="ignore_channel")
                ]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            message_text = f"🔍 تم اكتشاف معرف قناة محتمل:\n\n📝 المعرف المدخل: {text}\n"

            if processed_channel != text:
                message_text += f"🔄 المعرف المعالج: {processed_channel}\n"

            message_text += "\nهل تريد حفظه كمعرف قناة Telegram؟"

            await update.message.reply_text(message_text, reply_markup=reply_markup)

    def _is_channel_identifier(self, text: str) -> bool:
        """التحقق من أن النص يشبه معرف قناة"""
        # معرف يبدأ بـ @
        if text.startswith('@') and len(text) > 1:
            return True

        # معرف رقمي (سالب عادة للقنوات)
        if text.lstrip('-').isdigit() and len(text) > 5:
            return True

        # رابط قناة
        if 't.me/' in text or 'telegram.me/' in text:
            return True

        return False

    def _extract_channel_username(self, text: str) -> str:
        """استخراج اسم المستخدم من رابط القناة"""
        # إذا كان رابط
        if 't.me/' in text:
            return '@' + text.split('t.me/')[-1].split('?')[0].split('/')[0]
        elif 'telegram.me/' in text:
            return '@' + text.split('telegram.me/')[-1].split('?')[0].split('/')[0]

        # إذا كان معرف عادي
        return text

    async def save_channel_from_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE, channel_input: str):
        """حفظ معرف القناة من الزر"""
        try:
            # تحديث الرسالة لإظهار حالة المعالجة
            await update.callback_query.message.edit_text("⏳ جاري حفظ معرف القناة والحصول على المعرف الرقمي...")

            # استخراج اسم المستخدم من الرابط إذا لزم الأمر
            processed_channel = self._extract_channel_username(channel_input)

            # محاولة الحصول على معرف القناة الرقمي
            channel_id = await self._get_channel_id(processed_channel)

            if channel_id:
                # حفظ معرف القناة الرقمي
                if self._update_env_file('TELEGRAM_CHANNEL_ID', str(channel_id), update.effective_user.id):
                    await update.callback_query.message.edit_text(
                        f"✅ تم حفظ معرف قناة Telegram بنجاح!\n\n"
                        f"📝 المعرف المدخل: {channel_input}\n"
                        f"🔄 المعرف المعالج: {processed_channel}\n"
                        f"🆔 المعرف الرقمي: {channel_id}\n\n"
                        f"💡 تم حفظ المعرف الرقمي تلقائياً للاستخدام في النشر\n"
                        f"🔄 استخدم 'إعادة تحميل الإعدادات' لتطبيق التغييرات"
                    )
                    await self.safe_answer_callback_query(update.callback_query, "✅ تم حفظ معرف القناة")
                else:
                    await update.callback_query.message.edit_text("❌ فشل في حفظ الإعدادات")
                    await self.safe_answer_callback_query(update.callback_query, "❌ فشل في الحفظ")
            else:
                # إذا فشل الحصول على المعرف الرقمي، احفظ المعرف المعالج
                if self._update_env_file('TELEGRAM_CHANNEL_ID', processed_channel, update.effective_user.id):
                    await update.callback_query.message.edit_text(
                        f"⚠️ تم حفظ معرف القناة: {processed_channel}\n\n"
                        f"📝 المعرف المدخل: {channel_input}\n"
                        f"❗ لم يتمكن البوت من الحصول على المعرف الرقمي تلقائياً.\n"
                        f"💡 تأكد من أن البوت مضاف كمشرف في القناة للحصول على المعرف الرقمي.\n"
                        f"🔄 استخدم 'إعادة تحميل الإعدادات' لتطبيق التغييرات"
                    )
                    await self.safe_answer_callback_query(update.callback_query, "⚠️ تم حفظ المعرف (بدون ID رقمي)")
                else:
                    await update.callback_query.message.edit_text("❌ فشل في حفظ الإعدادات")
                    await self.safe_answer_callback_query(update.callback_query, "❌ فشل في الحفظ")

        except Exception as e:
            logger.error(f"خطأ في حفظ معرف القناة: {e}")
            await update.callback_query.message.edit_text(f"❌ خطأ في حفظ معرف القناة: {str(e)}")
            await self.safe_answer_callback_query(update.callback_query, "❌ حدث خطأ")

    async def update_channel_id_automatically(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تحديث معرف القناة تلقائياً للحصول على المعرف الرقمي"""
        try:
            current_channel = self.config.TELEGRAM_CHANNEL_ID
            if not current_channel:
                await self.safe_answer_callback_query(update.callback_query, "❌ لا يوجد معرف قناة محفوظ")
                return

            # تحديث الرسالة لإظهار حالة المعالجة
            await update.callback_query.message.edit_text(
                f"⏳ جاري تحديث معرف القناة...\n\n"
                f"📝 المعرف الحالي: {current_channel}\n"
                f"🔍 البحث عن المعرف الرقمي..."
            )

            # محاولة الحصول على معرف القناة الرقمي
            numeric_id = await self._get_channel_id(current_channel)

            if numeric_id and str(numeric_id) != current_channel:
                # حفظ المعرف الرقمي الجديد
                if self._update_env_file('TELEGRAM_CHANNEL_ID', str(numeric_id), update.effective_user.id):
                    await update.callback_query.message.edit_text(
                        f"✅ تم تحديث معرف القناة بنجاح!\n\n"
                        f"📝 المعرف السابق: {current_channel}\n"
                        f"🆔 المعرف الرقمي الجديد: {numeric_id}\n\n"
                        f"💡 تم حفظ المعرف الرقمي للاستخدام في النشر\n"
                        f"🔄 استخدم 'إعادة تحميل الإعدادات' لتطبيق التغييرات"
                    )
                    await self.safe_answer_callback_query(update.callback_query, "✅ تم التحديث بنجاح")
                else:
                    await update.callback_query.message.edit_text("❌ فشل في حفظ المعرف الجديد")
                    await self.safe_answer_callback_query(update.callback_query, "❌ فشل في الحفظ")
            elif str(numeric_id) == current_channel:
                await update.callback_query.message.edit_text(
                    f"ℹ️ المعرف الحالي رقمي بالفعل\n\n"
                    f"🆔 المعرف: {current_channel}\n\n"
                    f"✅ لا حاجة للتحديث"
                )
                await self.safe_answer_callback_query(update.callback_query, "ℹ️ المعرف رقمي بالفعل")
            else:
                await update.callback_query.message.edit_text(
                    f"❌ فشل في الحصول على المعرف الرقمي\n\n"
                    f"📝 المعرف الحالي: {current_channel}\n\n"
                    f"💡 تأكد من:\n"
                    f"• البوت مضاف كمشرف في القناة\n"
                    f"• معرف القناة صحيح\n"
                    f"• القناة عامة أو البوت له صلاحيات"
                )
                await self.safe_answer_callback_query(update.callback_query, "❌ فشل في الحصول على المعرف")

        except Exception as e:
            logger.error(f"خطأ في تحديث معرف القناة تلقائياً: {e}")
            await update.callback_query.message.edit_text(f"❌ خطأ في التحديث: {str(e)}")
            await self.safe_answer_callback_query(update.callback_query, "❌ حدث خطأ")

    # دوال تعديل إعدادات Instagram
    async def set_enable_instagram(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تفعيل/تعطيل Instagram"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد true أو false\nمثال: /set_enable_instagram true")
            return

        value = context.args[0].lower()
        if value not in ['true', 'false']:
            await update.message.reply_text("❌ القيمة يجب أن تكون true أو false")
            return

        if self._update_env_file('ENABLE_INSTAGRAM', value, update.effective_user.id):
            await update.message.reply_text(f"✅ تم تعديل تفعيل Instagram إلى: {value}")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")

    async def set_instagram_username(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تعديل اسم مستخدم Instagram"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد اسم المستخدم\nمثال: /set_instagram_username myusername")
            return

        username = context.args[0]
        if self._update_env_file('INSTAGRAM_USERNAME', username, update.effective_user.id):
            await update.message.reply_text("✅ تم تحديث اسم مستخدم Instagram بنجاح")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")

    async def set_instagram_password(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تعديل كلمة مرور Instagram"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد كلمة المرور\nمثال: /set_instagram_password mypassword")
            return

        password = context.args[0]
        if self._update_env_file('INSTAGRAM_PASSWORD', password, update.effective_user.id):
            await update.message.reply_text("✅ تم تحديث كلمة مرور Instagram بنجاح")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")

    # دوال تعديل إعدادات Facebook
    async def set_enable_facebook(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تفعيل/تعطيل Facebook"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد true أو false\nمثال: /set_enable_facebook true")
            return

        value = context.args[0].lower()
        if value not in ['true', 'false']:
            await update.message.reply_text("❌ القيمة يجب أن تكون true أو false")
            return

        if self._update_env_file('ENABLE_FACEBOOK', value, update.effective_user.id):
            await update.message.reply_text(f"✅ تم تعديل تفعيل Facebook إلى: {value}")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")

    async def set_facebook_access_token(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تعديل رمز وصول Facebook"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد رمز الوصول\nمثال: /set_facebook_access_token EAABwz...")
            return

        token = context.args[0]
        if self._update_env_file('FACEBOOK_ACCESS_TOKEN', token, update.effective_user.id):
            await update.message.reply_text("✅ تم تحديث رمز وصول Facebook بنجاح")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")

    async def set_facebook_page_id(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تعديل معرف صفحة Facebook"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد معرف الصفحة\nمثال: /set_facebook_page_id 123456789")
            return

        page_id = context.args[0]
        if self._update_env_file('FACEBOOK_PAGE_ID', page_id, update.effective_user.id):
            await update.message.reply_text("✅ تم تحديث معرف صفحة Facebook بنجاح")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")

    # دالة تعديل إعدادات Gemini
    async def set_gemini_api_key(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تعديل مفتاح Gemini API"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        if not context.args:
            await update.message.reply_text("❌ يرجى تحديد مفتاح API\nمثال: /set_gemini_api_key AIzaSy...")
            return

        api_key = context.args[0]
        if self._update_env_file('GEMINI_API_KEY', api_key, update.effective_user.id):
            await update.message.reply_text("✅ تم تحديث مفتاح Gemini API بنجاح")
        else:
            await update.message.reply_text("❌ فشل في تحديث الإعدادات")
    
    async def auto_publish_task(self):
        """مهمة النشر التلقائي"""
        while True:
            try:
                if self.auto_publish_enabled:
                    if (self.last_publish_time is None or 
                        datetime.now() - self.last_publish_time >= timedelta(seconds=self.publish_interval)):
                        
                        logger.info("بدء النشر التلقائي...")
                        
                        # تنفيذ النشر
                        try:
                            verse = self.quran_manager.get_random_verse()
                            if verse:
                                if self.gemini_client:
                                    try:
                                        phrase = self.gemini_client.get_enhanced_phrase()
                                    except:
                                        phrase = self.quran_manager.get_random_inspirational_phrase()
                                else:
                                    phrase = self.quran_manager.get_random_inspirational_phrase()
                                
                                image_path = self.image_generator.generate_verse_image(verse, phrase)

                                if image_path and os.path.exists(image_path):
                                    content = {
                                        'verse': verse,
                                        'phrase': phrase,
                                        'caption': f"📖 {verse['surah']} - آية {verse['verse_number']}\n\n💭 {phrase}"
                                    }

                                    # النشر على جميع المنصات المفعلة
                                    success_platforms = []

                                    # Telegram
                                    if self.telegram_publisher and self.platform_settings['telegram']['enabled']:
                                        try:
                                            if self.telegram_publisher.publish_verse_content(image_path, content):
                                                success_platforms.append('Telegram')
                                        except Exception as e:
                                            logger.error(f"خطأ في النشر التلقائي على Telegram: {e}")

                                    # Instagram
                                    if self.instagram_publisher and self.platform_settings['instagram']['enabled']:
                                        try:
                                            if self.instagram_publisher.publish_verse_content(image_path, content):
                                                success_platforms.append('Instagram')
                                        except Exception as e:
                                            logger.error(f"خطأ في النشر التلقائي على Instagram: {e}")

                                    # Facebook
                                    if self.facebook_publisher and self.platform_settings['facebook']['enabled']:
                                        try:
                                            if self.facebook_publisher.publish_verse_content(image_path, content):
                                                success_platforms.append('Facebook')
                                        except Exception as e:
                                            logger.error(f"خطأ في النشر التلقائي على Facebook: {e}")

                                    # تحديث وقت آخر نشر إذا نجح على منصة واحدة على الأقل
                                    if success_platforms:
                                        self.last_publish_time = datetime.now()
                                        logger.info(f"تم النشر التلقائي بنجاح على: {', '.join(success_platforms)}")
                                    else:
                                        logger.error("فشل النشر التلقائي على جميع المنصات")

                                    # تنظيف الصورة
                                    try:
                                        if os.path.exists(image_path):
                                            os.remove(image_path)
                                    except:
                                        pass
                        
                        except Exception as e:
                            logger.error(f"خطأ في النشر التلقائي: {e}")
                
                # انتظار دقيقة واحدة قبل التحقق مرة أخرى
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"خطأ في مهمة النشر التلقائي: {e}")
                await asyncio.sleep(60)
    
    async def run_async(self):
        """تشغيل البوت بشكل غير متزامن"""
        if not self.bot_token:
            logger.error("رمز البوت غير متوفر")
            return

        # إنشاء التطبيق
        self.app = Application.builder().token(self.bot_token).build()

        # إضافة معالجات الأوامر
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("status", self.status_command))
        self.app.add_handler(CommandHandler("publish", self.publish_now))
        self.app.add_handler(CommandHandler("settings", self.show_settings))
        self.app.add_handler(CommandHandler("stats", self.show_statistics))
        self.app.add_handler(CommandHandler("help", self.show_help))
        self.app.add_handler(CommandHandler("platforms", self.show_platforms))
        self.app.add_handler(CommandHandler("toggle_auto", self.toggle_auto_publish))
        self.app.add_handler(CommandHandler("set_interval", self.set_publish_interval))
        self.app.add_handler(CommandHandler("auto_status", self.auto_status_command))
        self.app.add_handler(CommandHandler("force_publish", self.force_publish_command))
        self.app.add_handler(CommandHandler("quick_setup", self.quick_setup_command))
        self.app.add_handler(CommandHandler("diagnose_platforms", self.diagnose_platforms_command))
        self.app.add_handler(CommandHandler("test_platforms", self.test_platforms_command))

        # معالجات تعديل الإعدادات
        self.app.add_handler(CommandHandler("set_enable_telegram", self.set_enable_telegram))
        self.app.add_handler(CommandHandler("set_telegram_bot_token", self.set_telegram_bot_token))
        self.app.add_handler(CommandHandler("set_telegram_channel_id", self.set_telegram_channel_id))
        self.app.add_handler(CommandHandler("set_enable_instagram", self.set_enable_instagram))
        self.app.add_handler(CommandHandler("set_instagram_username", self.set_instagram_username))
        self.app.add_handler(CommandHandler("set_instagram_password", self.set_instagram_password))
        self.app.add_handler(CommandHandler("set_enable_facebook", self.set_enable_facebook))
        self.app.add_handler(CommandHandler("set_facebook_access_token", self.set_facebook_access_token))
        self.app.add_handler(CommandHandler("set_facebook_page_id", self.set_facebook_page_id))
        self.app.add_handler(CommandHandler("set_gemini_api_key", self.set_gemini_api_key))
        self.app.add_handler(CommandHandler("config_summary", self.show_config_summary))
        self.app.add_handler(CommandHandler("config_history", self.show_config_history))

        # معالج الأزرار
        self.app.add_handler(CallbackQueryHandler(self.button_callback))

        logger.info("تم بدء تشغيل بوت التحكم")

        # تشغيل البوت
        self.app.run_polling()

    def run(self):
        """تشغيل البوت (الطريقة التقليدية)"""
        if not self.bot_token:
            logger.error("رمز البوت غير متوفر")
            return

        # إنشاء التطبيق
        self.app = Application.builder().token(self.bot_token).build()

        # إضافة معالجات الأوامر
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("status", self.status_command))
        self.app.add_handler(CommandHandler("publish", self.publish_now))
        self.app.add_handler(CommandHandler("settings", self.show_settings))
        self.app.add_handler(CommandHandler("stats", self.show_statistics))
        self.app.add_handler(CommandHandler("help", self.show_help))
        self.app.add_handler(CommandHandler("platforms", self.show_platforms))
        self.app.add_handler(CommandHandler("toggle_auto", self.toggle_auto_publish))
        self.app.add_handler(CommandHandler("set_interval", self.set_publish_interval))
        self.app.add_handler(CommandHandler("auto_status", self.auto_status_command))
        self.app.add_handler(CommandHandler("force_publish", self.force_publish_command))
        self.app.add_handler(CommandHandler("quick_setup", self.quick_setup_command))
        self.app.add_handler(CommandHandler("diagnose_platforms", self.diagnose_platforms_command))
        self.app.add_handler(CommandHandler("test_platforms", self.test_platforms_command))

        # معالجات تعديل الإعدادات
        self.app.add_handler(CommandHandler("set_enable_telegram", self.set_enable_telegram))
        self.app.add_handler(CommandHandler("set_telegram_bot_token", self.set_telegram_bot_token))
        self.app.add_handler(CommandHandler("set_telegram_channel_id", self.set_telegram_channel_id))
        self.app.add_handler(CommandHandler("set_enable_instagram", self.set_enable_instagram))
        self.app.add_handler(CommandHandler("set_instagram_username", self.set_instagram_username))
        self.app.add_handler(CommandHandler("set_instagram_password", self.set_instagram_password))
        self.app.add_handler(CommandHandler("set_enable_facebook", self.set_enable_facebook))
        self.app.add_handler(CommandHandler("set_facebook_access_token", self.set_facebook_access_token))
        self.app.add_handler(CommandHandler("set_facebook_page_id", self.set_facebook_page_id))
        self.app.add_handler(CommandHandler("set_gemini_api_key", self.set_gemini_api_key))
        self.app.add_handler(CommandHandler("config_summary", self.show_config_summary))
        self.app.add_handler(CommandHandler("config_history", self.show_config_history))

        # معالج الأزرار
        self.app.add_handler(CallbackQueryHandler(self.button_callback))

        # معالج الرسائل النصية (لمعرفات القنوات)
        self.app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_text_message))

        logger.info("تم بدء تشغيل بوت التحكم")

        # تشغيل البوت
        self.app.run_polling()

    async def show_config_summary(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض ملخص الإعدادات"""
        try:
            summary = self.config_manager.get_config_summary()

            keyboard = [
                [InlineKeyboardButton("🔙 العودة للإعدادات", callback_data="configure_platforms")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            if update.callback_query:
                await update.callback_query.message.reply_text(summary, reply_markup=reply_markup)
            else:
                await update.message.reply_text(summary, reply_markup=reply_markup)

        except Exception as e:
            logger.error(f"خطأ في عرض ملخص الإعدادات: {e}")
            error_msg = "❌ خطأ في عرض ملخص الإعدادات"
            if update.callback_query:
                await update.callback_query.message.reply_text(error_msg)
            else:
                await update.message.reply_text(error_msg)

    async def show_config_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض تاريخ تغييرات الإعدادات"""
        try:
            history = self.config_manager.get_config_history(10)

            if not history:
                history_text = "📜 تاريخ التغييرات:\n\nلا توجد تغييرات مسجلة بعد."
            else:
                history_text = "📜 آخر 10 تغييرات في الإعدادات:\n\n"

                for i, entry in enumerate(reversed(history), 1):
                    timestamp = entry.get('timestamp', 'غير محدد')
                    user_id = entry.get('user_id', 'غير محدد')
                    key = entry.get('key', 'غير محدد')
                    old_value = entry.get('old_value', '')
                    new_value = entry.get('new_value', '')

                    # إخفاء القيم الحساسة
                    if any(sensitive in key.upper() for sensitive in ['TOKEN', 'PASSWORD', 'KEY']):
                        old_value = '***' if old_value else 'فارغ'
                        new_value = '***' if new_value else 'فارغ'

                    history_text += f"{i}. {key}\n"
                    history_text += f"   📅 {timestamp[:19]}\n"
                    history_text += f"   👤 المستخدم: {user_id}\n"
                    history_text += f"   🔄 {old_value} ← {new_value}\n\n"

            keyboard = [
                [InlineKeyboardButton("🔙 العودة للإعدادات", callback_data="configure_platforms")]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            if update.callback_query:
                await update.callback_query.message.reply_text(history_text, reply_markup=reply_markup)
            else:
                await update.message.reply_text(history_text, reply_markup=reply_markup)

        except Exception as e:
            logger.error(f"خطأ في عرض تاريخ الإعدادات: {e}")
            error_msg = "❌ خطأ في عرض تاريخ الإعدادات"
            if update.callback_query:
                await update.callback_query.message.reply_text(error_msg)
            else:
                await update.message.reply_text(error_msg)

def main():
    """الدالة الرئيسية"""
    print("🤖 بدء تشغيل بوت التحكم في روبوت نشر الآيات القرآنية")
    print("=" * 60)

    bot_controller = TelegramBotController()
    bot_controller.run()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البوت")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
