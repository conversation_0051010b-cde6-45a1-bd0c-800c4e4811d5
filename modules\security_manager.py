import os
import json
import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import ipaddress
from cryptography.fernet import Fernet
import jwt
import logging

class SecurityManager:
    """مدير الأمان المتقدم لحماية البيانات والمحتوى"""
    
    def __init__(self, secret_key: Optional[str] = None):
        self.secret_key = secret_key or os.getenv('SECRET_KEY', self._generate_secret_key())
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # إعدادات الأمان
        self.security_config = {
            'max_login_attempts': 5,
            'lockout_duration_minutes': 30,
            'session_timeout_hours': 24,
            'password_min_length': 8,
            'require_special_chars': True,
            'enable_2fa': False,
            'allowed_ip_ranges': [],
            'rate_limit_requests_per_minute': 60,
            'enable_audit_log': True
        }
        
        # تتبع محاولات تسجيل الدخول
        self.login_attempts = {}
        self.blocked_ips = {}
        self.active_sessions = {}
        self.audit_log = []
        
        # إعداد نظام السجلات
        self.logger = self._setup_logger()
        
        # تحميل البيانات المحفوظة
        self._load_security_data()
    
    def _generate_secret_key(self) -> str:
        """توليد مفتاح سري عشوائي"""
        return secrets.token_urlsafe(32)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """الحصول على مفتاح التشفير أو إنشاؤه"""
        key_file = "data/encryption.key"
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            os.makedirs(os.path.dirname(key_file), exist_ok=True)
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def _setup_logger(self) -> logging.Logger:
        """إعداد نظام السجلات الأمنية"""
        logger = logging.getLogger('security')
        logger.setLevel(logging.INFO)
        
        # إنشاء معالج الملف
        os.makedirs('logs', exist_ok=True)
        file_handler = logging.FileHandler('logs/security.log', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # تنسيق السجلات
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        return logger
    
    def _load_security_data(self):
        """تحميل بيانات الأمان المحفوظة"""
        try:
            security_file = "data/security_data.json"
            if os.path.exists(security_file):
                with open(security_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.login_attempts = data.get('login_attempts', {})
                    self.blocked_ips = data.get('blocked_ips', {})
                    self.audit_log = data.get('audit_log', [])
        except Exception as e:
            self.logger.error(f"خطأ في تحميل بيانات الأمان: {e}")
    
    def _save_security_data(self):
        """حفظ بيانات الأمان"""
        try:
            security_file = "data/security_data.json"
            os.makedirs(os.path.dirname(security_file), exist_ok=True)
            
            data = {
                'login_attempts': self.login_attempts,
                'blocked_ips': self.blocked_ips,
                'audit_log': self.audit_log[-1000:]  # الاحتفاظ بآخر 1000 سجل فقط
            }
            
            with open(security_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ بيانات الأمان: {e}")
    
    def encrypt_data(self, data: str) -> str:
        """تشفير البيانات"""
        try:
            encrypted_data = self.cipher.encrypt(data.encode('utf-8'))
            return encrypted_data.decode('utf-8')
        except Exception as e:
            self.logger.error(f"خطأ في تشفير البيانات: {e}")
            return data
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        try:
            decrypted_data = self.cipher.decrypt(encrypted_data.encode('utf-8'))
            return decrypted_data.decode('utf-8')
        except Exception as e:
            self.logger.error(f"خطأ في فك تشفير البيانات: {e}")
            return encrypted_data
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """التحقق من قوة كلمة المرور"""
        issues = []
        score = 0
        
        # طول كلمة المرور
        if len(password) < self.security_config['password_min_length']:
            issues.append(f"كلمة المرور يجب أن تكون {self.security_config['password_min_length']} أحرف على الأقل")
        else:
            score += 20
        
        # وجود أحرف كبيرة وصغيرة
        if any(c.isupper() for c in password) and any(c.islower() for c in password):
            score += 20
        else:
            issues.append("يجب أن تحتوي على أحرف كبيرة وصغيرة")
        
        # وجود أرقام
        if any(c.isdigit() for c in password):
            score += 20
        else:
            issues.append("يجب أن تحتوي على أرقام")
        
        # وجود رموز خاصة
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if self.security_config['require_special_chars']:
            if any(c in special_chars for c in password):
                score += 20
            else:
                issues.append("يجب أن تحتوي على رموز خاصة")
        
        # عدم وجود كلمات شائعة
        common_passwords = ['password', '123456', 'admin', 'user', 'qwerty']
        if password.lower() not in common_passwords:
            score += 20
        else:
            issues.append("كلمة المرور شائعة جداً")
        
        strength_level = "ضعيف"
        if score >= 80:
            strength_level = "قوي جداً"
        elif score >= 60:
            strength_level = "قوي"
        elif score >= 40:
            strength_level = "متوسط"
        
        return {
            'is_valid': len(issues) == 0,
            'score': score,
            'strength': strength_level,
            'issues': issues
        }
    
    def check_ip_allowed(self, ip_address: str) -> bool:
        """التحقق من السماح لعنوان IP"""
        # التحقق من القائمة المحظورة
        if ip_address in self.blocked_ips:
            block_info = self.blocked_ips[ip_address]
            if datetime.now() < datetime.fromisoformat(block_info['blocked_until']):
                return False
            else:
                # انتهت فترة الحظر
                del self.blocked_ips[ip_address]
                self._save_security_data()
        
        # التحقق من النطاقات المسموحة
        if self.security_config['allowed_ip_ranges']:
            try:
                ip = ipaddress.ip_address(ip_address)
                for range_str in self.security_config['allowed_ip_ranges']:
                    if ip in ipaddress.ip_network(range_str, strict=False):
                        return True
                return False
            except ValueError:
                return False
        
        return True
    
    def record_login_attempt(self, username: str, ip_address: str, success: bool, user_agent: str = ""):
        """تسجيل محاولة تسجيل دخول"""
        attempt_key = f"{username}:{ip_address}"
        current_time = datetime.now()
        
        # تسجيل المحاولة في السجل
        log_entry = {
            'timestamp': current_time.isoformat(),
            'event': 'login_attempt',
            'username': username,
            'ip_address': ip_address,
            'success': success,
            'user_agent': user_agent
        }
        
        self.audit_log.append(log_entry)
        self.logger.info(f"محاولة تسجيل دخول - المستخدم: {username}, IP: {ip_address}, النجاح: {success}")
        
        if not success:
            # تتبع المحاولات الفاشلة
            if attempt_key not in self.login_attempts:
                self.login_attempts[attempt_key] = {
                    'count': 0,
                    'first_attempt': current_time.isoformat(),
                    'last_attempt': current_time.isoformat()
                }
            
            self.login_attempts[attempt_key]['count'] += 1
            self.login_attempts[attempt_key]['last_attempt'] = current_time.isoformat()
            
            # حظر IP إذا تجاوز الحد المسموح
            if self.login_attempts[attempt_key]['count'] >= self.security_config['max_login_attempts']:
                self._block_ip(ip_address, f"تجاوز الحد المسموح من محاولات تسجيل الدخول للمستخدم {username}")
        else:
            # مسح المحاولات الفاشلة عند النجاح
            if attempt_key in self.login_attempts:
                del self.login_attempts[attempt_key]
        
        self._save_security_data()
    
    def _block_ip(self, ip_address: str, reason: str):
        """حظر عنوان IP"""
        blocked_until = datetime.now() + timedelta(minutes=self.security_config['lockout_duration_minutes'])
        
        self.blocked_ips[ip_address] = {
            'blocked_at': datetime.now().isoformat(),
            'blocked_until': blocked_until.isoformat(),
            'reason': reason
        }
        
        self.logger.warning(f"تم حظر IP {ip_address}: {reason}")
    
    def generate_secure_token(self, user_id: str, additional_data: Dict = None) -> str:
        """توليد رمز آمن للمستخدم"""
        payload = {
            'user_id': user_id,
            'issued_at': datetime.now().timestamp(),
            'expires_at': (datetime.now() + timedelta(hours=self.security_config['session_timeout_hours'])).timestamp(),
            'token_id': secrets.token_hex(16)
        }
        
        if additional_data:
            payload.update(additional_data)
        
        token = jwt.encode(payload, self.secret_key, algorithm='HS256')
        
        # حفظ الرمز في الجلسات النشطة
        self.active_sessions[payload['token_id']] = {
            'user_id': user_id,
            'created_at': datetime.now().isoformat(),
            'last_activity': datetime.now().isoformat(),
            'ip_address': additional_data.get('ip_address', '') if additional_data else ''
        }
        
        return token
    
    def validate_token(self, token: str, ip_address: str = "") -> Optional[Dict]:
        """التحقق من صحة الرمز"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            
            # التحقق من انتهاء الصلاحية
            if datetime.now().timestamp() > payload['expires_at']:
                return None
            
            # التحقق من وجود الرمز في الجلسات النشطة
            token_id = payload.get('token_id')
            if token_id not in self.active_sessions:
                return None
            
            # تحديث آخر نشاط
            self.active_sessions[token_id]['last_activity'] = datetime.now().isoformat()
            
            return payload
            
        except jwt.InvalidTokenError:
            return None
    
    def revoke_token(self, token: str):
        """إلغاء رمز الوصول"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            token_id = payload.get('token_id')
            
            if token_id in self.active_sessions:
                del self.active_sessions[token_id]
                self.logger.info(f"تم إلغاء الرمز للمستخدم {payload.get('user_id')}")
                
        except jwt.InvalidTokenError:
            pass
    
    def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        current_time = datetime.now()
        expired_sessions = []
        
        for token_id, session in self.active_sessions.items():
            last_activity = datetime.fromisoformat(session['last_activity'])
            if current_time - last_activity > timedelta(hours=self.security_config['session_timeout_hours']):
                expired_sessions.append(token_id)
        
        for token_id in expired_sessions:
            del self.active_sessions[token_id]
        
        if expired_sessions:
            self.logger.info(f"تم تنظيف {len(expired_sessions)} جلسة منتهية الصلاحية")
    
    def get_security_report(self) -> Dict:
        """الحصول على تقرير الأمان"""
        current_time = datetime.now()
        
        # إحصائيات المحاولات الفاشلة
        failed_attempts_24h = len([
            log for log in self.audit_log
            if log.get('event') == 'login_attempt' 
            and not log.get('success')
            and datetime.fromisoformat(log['timestamp']) > current_time - timedelta(hours=24)
        ])
        
        # الجلسات النشطة
        active_sessions_count = len(self.active_sessions)
        
        # عناوين IP المحظورة
        blocked_ips_count = len(self.blocked_ips)
        
        return {
            'failed_login_attempts_24h': failed_attempts_24h,
            'active_sessions': active_sessions_count,
            'blocked_ips': blocked_ips_count,
            'security_config': self.security_config,
            'last_security_events': self.audit_log[-10:],  # آخر 10 أحداث
            'recommendations': self._generate_security_recommendations()
        }
    
    def _generate_security_recommendations(self) -> List[str]:
        """توليد توصيات أمنية"""
        recommendations = []
        
        if not self.security_config['enable_2fa']:
            recommendations.append("يُنصح بتفعيل المصادقة الثنائية لزيادة الأمان")
        
        if not self.security_config['allowed_ip_ranges']:
            recommendations.append("يُنصح بتحديد نطاقات IP مسموحة للوصول")
        
        if len(self.blocked_ips) > 10:
            recommendations.append("عدد كبير من عناوين IP محظورة - قد تحتاج لمراجعة إعدادات الأمان")
        
        if self.security_config['password_min_length'] < 12:
            recommendations.append("يُنصح بزيادة الحد الأدنى لطول كلمة المرور إلى 12 حرف")
        
        return recommendations
    
    def update_security_config(self, new_config: Dict):
        """تحديث إعدادات الأمان"""
        self.security_config.update(new_config)
        self.logger.info("تم تحديث إعدادات الأمان")
        
        # حفظ الإعدادات
        config_file = "data/security_config.json"
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.security_config, f, ensure_ascii=False, indent=2)
