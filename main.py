#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
روبوت نشر الآيات القرآنية - التطبيق الرئيسي
نسخة مفتوحة المصدر بدون واجهة ويب - التحكم عبر Telegram فقط
"""

import os
import sys
import asyncio
import logging
import signal
from datetime import datetime

# إعداد الترميز للعربية
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# استيراد الوحدات المخصصة
from config import get_config
from telegram_bot_controller import TelegramBotController

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quran_bot.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class QuranBotApp:
    def __init__(self):
        self.config = get_config()
        self.bot_controller = None
        self.running = False
    
    def check_requirements(self) -> bool:
        """التحقق من المتطلبات الأساسية"""
        logger.info("🔍 التحقق من المتطلبات الأساسية...")
        
        errors = []
        
        # التحقق من رمز Telegram Bot
        if not self.config.TELEGRAM_BOT_TOKEN:
            errors.append("TELEGRAM_BOT_TOKEN مطلوب")
        
        # التحقق من معرف القناة
        if not self.config.TELEGRAM_CHANNEL_ID:
            errors.append("TELEGRAM_CHANNEL_ID مطلوب")
        
        # التحقق من مفتاح Gemini (اختياري)
        if not self.config.GEMINI_API_KEY:
            logger.warning("⚠️ GEMINI_API_KEY غير متوفر - سيتم استخدام العبارات الاحتياطية")
        
        # التحقق من وجود الملفات المطلوبة
        required_dirs = ['backgrounds', 'fonts', 'data']
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                try:
                    os.makedirs(dir_name, exist_ok=True)
                    logger.info(f"✅ تم إنشاء مجلد {dir_name}")
                except Exception as e:
                    errors.append(f"فشل في إنشاء مجلد {dir_name}: {e}")
        
        if errors:
            logger.error("❌ أخطاء في الإعدادات:")
            for error in errors:
                logger.error(f"   - {error}")
            return False
        
        logger.info("✅ جميع المتطلبات متوفرة")
        return True
    
    def display_startup_info(self):
        """عرض معلومات البدء"""
        print("\n" + "=" * 60)
        print("🕌 روبوت نشر الآيات القرآنية")
        print("=" * 60)
        print("📱 نسخة مفتوحة المصدر - التحكم عبر Telegram")
        print(f"🌍 البيئة: {self.config.ENVIRONMENT}")
        print(f"📊 قناة النشر: {self.config.TELEGRAM_CHANNEL_ID}")
        print(f"⏰ فترة النشر: {self.config.PUBLISH_INTERVAL_HOURS} ساعة")
        print(f"🤖 Gemini AI: {'✅ مفعل' if self.config.GEMINI_API_KEY else '❌ معطل'}")
        print("=" * 60)
        print("🎯 المميزات:")
        print("  • نشر تلقائي للآيات القرآنية")
        print("  • تحكم كامل عبر بوت Telegram")
        print("  • توليد صور جميلة للآيات")
        print("  • ذكاء اصطناعي لتوليد العبارات الإلهامية")
        print("  • إحصائيات ومراقبة شاملة")
        print("=" * 60)
        print("📋 للتحكم في البوت:")
        print("  1. ابحث عن البوت في Telegram")
        print("  2. ابدأ محادثة معه بالأمر /start")
        print("  3. استخدم الأوامر والأزرار للتحكم")
        print("=" * 60)
        print(f"🚀 بدء التشغيل في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
    
    def start(self):
        """بدء تشغيل التطبيق"""
        try:
            # التحقق من المتطلبات
            if not self.check_requirements():
                logger.error("❌ فشل في التحقق من المتطلبات")
                return False

            # عرض معلومات البدء
            self.display_startup_info()

            # إنشاء وتشغيل بوت التحكم
            logger.info("🤖 بدء تشغيل بوت التحكم...")
            self.bot_controller = TelegramBotController()

            self.running = True
            logger.info("✅ تم بدء التطبيق بنجاح")

            # تشغيل البوت
            self.bot_controller.run()

        except Exception as e:
            logger.error(f"❌ خطأ في بدء التطبيق: {e}")
            return False
    
    def stop(self):
        """إيقاف التطبيق"""
        logger.info("🛑 إيقاف التطبيق...")
        self.running = False

def signal_handler(signum, frame):
    """معالج إشارات النظام"""
    print("\n🛑 تم استلام إشارة الإيقاف...")
    sys.exit(0)

def main():
    """الدالة الرئيسية"""
    # تسجيل معالج الإشارات
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # إنشاء وتشغيل التطبيق
    app = QuranBotApp()
    app.start()

if __name__ == "__main__":
    try:
        # تشغيل التطبيق
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)
