import json
import os
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import requests
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import qrcode
from io import BytesIO
import base64

class UniqueFeatures:
    """مميزات فريدة ومبتكرة للأداة"""
    
    def __init__(self, user_id: str = "default"):
        self.user_id = user_id
        self.features_data = self._load_features_data()
        
        # إعدادات المميزات الفريدة
        self.smart_timing = SmartTimingEngine()
        self.mood_detector = MoodDetector()
        self.interactive_content = InteractiveContentGenerator()
        self.personalization = PersonalizationEngine(user_id)
        self.community_features = CommunityFeatures()
    
    def _load_features_data(self) -> Dict:
        """تحميل بيانات المميزات"""
        features_file = f"data/unique_features_{self.user_id}.json"
        try:
            if os.path.exists(features_file):
                with open(features_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        
        return {
            'smart_suggestions': [],
            'user_preferences': {},
            'interaction_history': [],
            'mood_patterns': {},
            'community_contributions': []
        }
    
    def _save_features_data(self):
        """حفظ بيانات المميزات"""
        features_file = f"data/unique_features_{self.user_id}.json"
        os.makedirs(os.path.dirname(features_file), exist_ok=True)
        with open(features_file, 'w', encoding='utf-8') as f:
            json.dump(self.features_data, f, ensure_ascii=False, indent=2)


class SmartTimingEngine:
    """محرك التوقيت الذكي لاختيار أفضل أوقات النشر"""
    
    def __init__(self):
        self.timing_patterns = {
            'فجر': {'start': 4, 'end': 6, 'mood': 'روحاني', 'themes': ['التوبة', 'الاستغفار', 'الدعاء']},
            'صباح': {'start': 6, 'end': 12, 'mood': 'نشيط', 'themes': ['الأمل', 'الرزق', 'النجاح']},
            'ظهر': {'start': 12, 'end': 15, 'mood': 'هادئ', 'themes': ['الصبر', 'التأمل', 'الحكمة']},
            'عصر': {'start': 15, 'end': 18, 'mood': 'تأملي', 'themes': ['الشكر', 'التدبر', 'السكينة']},
            'مغرب': {'start': 18, 'end': 20, 'mood': 'عائلي', 'themes': ['الرحمة', 'البر', 'الأسرة']},
            'ليل': {'start': 20, 'end': 24, 'mood': 'هادئ', 'themes': ['التوبة', 'الاستغفار', 'القيام']}
        }
        
        self.special_occasions = {
            'جمعة': {'themes': ['الجمعة', 'الدعاء', 'المغفرة'], 'boost': 1.5},
            'رمضان': {'themes': ['الصيام', 'القرآن', 'التقوى'], 'boost': 2.0},
            'حج': {'themes': ['الحج', 'التوبة', 'الوحدة'], 'boost': 1.8},
            'عيد': {'themes': ['الفرح', 'الشكر', 'التكافل'], 'boost': 1.6}
        }
    
    def get_optimal_posting_time(self, current_time: datetime = None) -> Dict:
        """الحصول على أفضل وقت للنشر"""
        if not current_time:
            current_time = datetime.now()
        
        hour = current_time.hour
        day_name = current_time.strftime('%A')
        
        # تحديد الفترة الزمنية
        current_period = None
        for period, config in self.timing_patterns.items():
            if config['start'] <= hour < config['end']:
                current_period = period
                break
        
        if not current_period:
            current_period = 'ليل'
        
        period_config = self.timing_patterns[current_period]
        
        # التحقق من المناسبات الخاصة
        occasion_boost = 1.0
        recommended_themes = period_config['themes'].copy()
        
        if day_name == 'Friday':
            occasion_boost = self.special_occasions['جمعة']['boost']
            recommended_themes.extend(self.special_occasions['جمعة']['themes'])
        
        return {
            'current_period': current_period,
            'mood': period_config['mood'],
            'recommended_themes': recommended_themes,
            'engagement_boost': occasion_boost,
            'optimal_next_hour': self._calculate_next_optimal_hour(current_time),
            'reasoning': f"الوقت الحالي ({current_period}) مناسب لمواضيع {', '.join(recommended_themes[:2])}"
        }
    
    def _calculate_next_optimal_hour(self, current_time: datetime) -> int:
        """حساب الساعة المثلى التالية للنشر"""
        # أوقات الذروة العامة
        peak_hours = [6, 9, 12, 15, 18, 21]
        current_hour = current_time.hour
        
        # البحث عن أقرب وقت ذروة
        for hour in peak_hours:
            if hour > current_hour:
                return hour
        
        # إذا لم نجد وقت ذروة اليوم، نعيد أول وقت ذروة في اليوم التالي
        return peak_hours[0]


class MoodDetector:
    """كاشف المزاج لتخصيص المحتوى حسب الحالة النفسية"""
    
    def __init__(self):
        self.mood_indicators = {
            'سعيد': {
                'keywords': ['فرح', 'سعادة', 'بشرى', 'نعمة', 'خير'],
                'colors': ['أصفر', 'برتقالي', 'أخضر فاتح'],
                'themes': ['الشكر', 'النعم', 'الفرح', 'البشرى']
            },
            'حزين': {
                'keywords': ['حزن', 'ألم', 'مصيبة', 'بلاء', 'ابتلاء'],
                'colors': ['أزرق', 'بنفسجي', 'رمادي'],
                'themes': ['الصبر', 'التسلية', 'الأجر', 'الحكمة']
            },
            'قلق': {
                'keywords': ['خوف', 'قلق', 'توتر', 'هم', 'غم'],
                'colors': ['أخضر', 'أزرق فاتح', 'بيج'],
                'themes': ['الطمأنينة', 'التوكل', 'السكينة', 'الأمان']
            },
            'متفائل': {
                'keywords': ['أمل', 'رجاء', 'تفاؤل', 'مستقبل', 'غد'],
                'colors': ['أزرق سماوي', 'أخضر', 'ذهبي'],
                'themes': ['الأمل', 'المستقبل', 'الرجاء', 'التفاؤل']
            },
            'روحاني': {
                'keywords': ['عبادة', 'ذكر', 'دعاء', 'تسبيح', 'قرآن'],
                'colors': ['أخضر داكن', 'ذهبي', 'أبيض'],
                'themes': ['العبادة', 'الذكر', 'التقوى', 'الإيمان']
            }
        }
    
    def detect_mood_from_time(self, current_time: datetime = None) -> str:
        """كشف المزاج من الوقت الحالي"""
        if not current_time:
            current_time = datetime.now()
        
        hour = current_time.hour
        
        # أوقات مختلفة تشير لمزاج مختلف
        if 4 <= hour < 7:
            return 'روحاني'  # وقت الفجر
        elif 7 <= hour < 12:
            return 'متفائل'  # الصباح
        elif 12 <= hour < 15:
            return 'سعيد'    # الظهيرة
        elif 15 <= hour < 18:
            return 'هادئ'    # العصر
        elif 18 <= hour < 21:
            return 'عائلي'   # المساء
        else:
            return 'روحاني'  # الليل
    
    def get_mood_based_content(self, mood: str) -> Dict:
        """الحصول على محتوى مناسب للمزاج"""
        mood_config = self.mood_indicators.get(mood, self.mood_indicators['متفائل'])
        
        return {
            'recommended_themes': mood_config['themes'],
            'color_palette': mood_config['colors'],
            'keywords': mood_config['keywords'],
            'content_style': self._get_content_style(mood)
        }
    
    def _get_content_style(self, mood: str) -> Dict:
        """الحصول على نمط المحتوى حسب المزاج"""
        styles = {
            'سعيد': {'tone': 'مفرح', 'font_style': 'bold', 'decoration': 'bright'},
            'حزين': {'tone': 'مواسي', 'font_style': 'gentle', 'decoration': 'soft'},
            'قلق': {'tone': 'مطمئن', 'font_style': 'calm', 'decoration': 'peaceful'},
            'متفائل': {'tone': 'محفز', 'font_style': 'dynamic', 'decoration': 'energetic'},
            'روحاني': {'tone': 'خاشع', 'font_style': 'elegant', 'decoration': 'sacred'}
        }
        
        return styles.get(mood, styles['متفائل'])


class InteractiveContentGenerator:
    """مولد المحتوى التفاعلي"""
    
    def __init__(self):
        self.interactive_types = [
            'qr_code_verse',
            'audio_verse',
            'verse_quiz',
            'reflection_prompt',
            'daily_challenge',
            'verse_chain'
        ]
    
    def generate_qr_verse(self, verse_text: str, verse_info: str) -> str:
        """إنشاء QR كود للآية مع رابط للتفسير"""
        # إنشاء رابط للآية
        verse_url = f"https://quran.com/search?q={verse_info}"
        
        # إنشاء QR كود
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(verse_url)
        qr.make(fit=True)
        
        # تحويل إلى صورة
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # حفظ كـ base64
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        qr_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return qr_base64
    
    def create_reflection_prompt(self, verse_theme: str) -> Dict:
        """إنشاء سؤال تأملي"""
        prompts = {
            'الصبر': [
                "كيف يمكن أن تطبق الصبر في حياتك اليومية؟",
                "ما هي المواقف التي تحتاج فيها للصبر أكثر؟",
                "كيف يساعدك الصبر على تحقيق أهدافك؟"
            ],
            'الأمل': [
                "ما الذي يملأ قلبك بالأمل؟",
                "كيف تحافظ على الأمل في الأوقات الصعبة؟",
                "ما هي أحلامك التي تسعى لتحقيقها؟"
            ],
            'الشكر': [
                "ما هي النعم التي تشكر الله عليها اليوم؟",
                "كيف يغير الشكر من نظرتك للحياة؟",
                "ما أصغر نعمة تستحق الشكر في يومك؟"
            ]
        }
        
        theme_prompts = prompts.get(verse_theme, prompts['الأمل'])
        selected_prompt = random.choice(theme_prompts)
        
        return {
            'prompt': selected_prompt,
            'theme': verse_theme,
            'engagement_type': 'reflection',
            'call_to_action': 'شاركنا تأملك في التعليقات'
        }
    
    def generate_daily_challenge(self, verse_theme: str) -> Dict:
        """إنشاء تحدي يومي مرتبط بالآية"""
        challenges = {
            'الصبر': [
                "تحدي اليوم: اصبر على شيء يزعجك دون شكوى",
                "تحدي اليوم: ساعد شخصاً يحتاج للصبر",
                "تحدي اليوم: اقرأ قصة عن الصبر وتأمل فيها"
            ],
            'الشكر': [
                "تحدي اليوم: اكتب 5 أشياء تشكر الله عليها",
                "تحدي اليوم: اشكر شخصاً أثر في حياتك إيجابياً",
                "تحدي اليوم: لاحظ النعم الصغيرة في يومك"
            ],
            'الأمل': [
                "تحدي اليوم: ابعث رسالة أمل لشخص محبط",
                "تحدي اليوم: ضع هدفاً جديداً واعمل عليه",
                "تحدي اليوم: ابحث عن الجانب الإيجابي في موقف صعب"
            ]
        }
        
        theme_challenges = challenges.get(verse_theme, challenges['الأمل'])
        selected_challenge = random.choice(theme_challenges)
        
        return {
            'challenge': selected_challenge,
            'theme': verse_theme,
            'duration': '24 ساعة',
            'reward': 'شعور بالإنجاز والقرب من الله',
            'hashtag': f'#تحدي_{verse_theme}'
        }


class PersonalizationEngine:
    """محرك التخصيص الشخصي"""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.user_profile = self._load_user_profile()
    
    def _load_user_profile(self) -> Dict:
        """تحميل ملف المستخدم الشخصي"""
        profile_file = f"data/user_profile_{self.user_id}.json"
        try:
            if os.path.exists(profile_file):
                with open(profile_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        
        return {
            'favorite_themes': [],
            'preferred_times': [],
            'interaction_patterns': {},
            'learning_preferences': {},
            'content_history': []
        }
    
    def learn_from_interaction(self, interaction_data: Dict):
        """التعلم من تفاعل المستخدم"""
        # تحليل نوع التفاعل
        if interaction_data.get('type') == 'like':
            theme = interaction_data.get('theme')
            if theme and theme not in self.user_profile['favorite_themes']:
                self.user_profile['favorite_themes'].append(theme)
        
        # تحليل أوقات التفاعل
        interaction_time = interaction_data.get('timestamp')
        if interaction_time:
            hour = datetime.fromisoformat(interaction_time).hour
            if hour not in self.user_profile['preferred_times']:
                self.user_profile['preferred_times'].append(hour)
        
        self._save_user_profile()
    
    def get_personalized_recommendations(self) -> Dict:
        """الحصول على توصيات شخصية"""
        recommendations = {
            'themes': self.user_profile['favorite_themes'][:3],
            'posting_times': self.user_profile['preferred_times'][:3],
            'content_style': self._determine_preferred_style(),
            'engagement_type': self._determine_preferred_engagement()
        }
        
        return recommendations
    
    def _determine_preferred_style(self) -> str:
        """تحديد النمط المفضل للمستخدم"""
        # تحليل تاريخ المحتوى لتحديد النمط المفضل
        styles = ['كلاسيكي', 'عصري', 'بسيط', 'فني']
        return random.choice(styles)  # مؤقتاً - سيتم تطويره لاحقاً
    
    def _determine_preferred_engagement(self) -> str:
        """تحديد نوع التفاعل المفضل"""
        engagement_types = ['تأملي', 'تفاعلي', 'تعليمي', 'تحفيزي']
        return random.choice(engagement_types)  # مؤقتاً
    
    def _save_user_profile(self):
        """حفظ ملف المستخدم"""
        profile_file = f"data/user_profile_{self.user_id}.json"
        os.makedirs(os.path.dirname(profile_file), exist_ok=True)
        with open(profile_file, 'w', encoding='utf-8') as f:
            json.dump(self.user_profile, f, ensure_ascii=False, indent=2)


class CommunityFeatures:
    """مميزات المجتمع والتفاعل الاجتماعي"""
    
    def __init__(self):
        self.community_data = self._load_community_data()
    
    def _load_community_data(self) -> Dict:
        """تحميل بيانات المجتمع"""
        community_file = "data/community_data.json"
        try:
            if os.path.exists(community_file):
                with open(community_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        
        return {
            'shared_verses': [],
            'community_challenges': [],
            'user_contributions': {},
            'trending_themes': [],
            'collective_goals': []
        }
    
    def add_community_verse(self, user_id: str, verse_data: Dict):
        """إضافة آية مشتركة من المجتمع"""
        community_verse = {
            'id': len(self.community_data['shared_verses']) + 1,
            'user_id': user_id,
            'verse_data': verse_data,
            'timestamp': datetime.now().isoformat(),
            'votes': 0,
            'status': 'pending'  # pending, approved, rejected
        }
        
        self.community_data['shared_verses'].append(community_verse)
        self._save_community_data()
        
        return community_verse['id']
    
    def create_community_challenge(self, challenge_data: Dict) -> str:
        """إنشاء تحدي مجتمعي"""
        challenge = {
            'id': f"challenge_{len(self.community_data['community_challenges']) + 1}",
            'title': challenge_data['title'],
            'description': challenge_data['description'],
            'theme': challenge_data.get('theme', 'عام'),
            'duration_days': challenge_data.get('duration_days', 7),
            'start_date': datetime.now().isoformat(),
            'participants': [],
            'progress': {}
        }
        
        self.community_data['community_challenges'].append(challenge)
        self._save_community_data()
        
        return challenge['id']
    
    def get_trending_content(self) -> Dict:
        """الحصول على المحتوى الرائج"""
        # تحليل الآيات الأكثر مشاركة
        popular_verses = sorted(
            self.community_data['shared_verses'],
            key=lambda x: x.get('votes', 0),
            reverse=True
        )[:5]
        
        # المواضيع الرائجة
        theme_counts = {}
        for verse in self.community_data['shared_verses']:
            theme = verse['verse_data'].get('theme', 'عام')
            theme_counts[theme] = theme_counts.get(theme, 0) + 1
        
        trending_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        
        return {
            'popular_verses': popular_verses,
            'trending_themes': [theme for theme, count in trending_themes],
            'active_challenges': len([c for c in self.community_data['community_challenges'] 
                                    if self._is_challenge_active(c)]),
            'community_stats': {
                'total_contributions': len(self.community_data['shared_verses']),
                'active_users': len(set(v['user_id'] for v in self.community_data['shared_verses']))
            }
        }
    
    def _is_challenge_active(self, challenge: Dict) -> bool:
        """التحقق من نشاط التحدي"""
        start_date = datetime.fromisoformat(challenge['start_date'])
        duration = timedelta(days=challenge['duration_days'])
        return datetime.now() < start_date + duration
    
    def _save_community_data(self):
        """حفظ بيانات المجتمع"""
        community_file = "data/community_data.json"
        os.makedirs(os.path.dirname(community_file), exist_ok=True)
        with open(community_file, 'w', encoding='utf-8') as f:
            json.dump(self.community_data, f, ensure_ascii=False, indent=2)
