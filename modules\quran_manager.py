import json
import random
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class QuranManager:
    """مدير الآيات القرآنية مع نظام منع التكرار"""
    
    def __init__(self, data_file: str = "data/quran_verses.json"):
        self.data_file = data_file
        self.verses_data = self._load_verses()
        self.used_verses_file = "data/used_verses.json"
        self.used_verses = self._load_used_verses()
    
    def _load_verses(self) -> Dict:
        """تحميل الآيات من ملف JSON"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"ملف الآيات غير موجود: {self.data_file}")
            return {"verses": [], "inspirational_phrases": []}
        except json.JSONDecodeError:
            print(f"خطأ في تحليل ملف JSON: {self.data_file}")
            return {"verses": [], "inspirational_phrases": []}
    
    def _load_used_verses(self) -> List[int]:
        """تحميل قائمة الآيات المستخدمة"""
        try:
            with open(self.used_verses_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('used_verse_ids', [])
        except FileNotFoundError:
            return []
        except json.JSONDecodeError:
            return []
    
    def _save_used_verses(self):
        """حفظ قائمة الآيات المستخدمة"""
        os.makedirs(os.path.dirname(self.used_verses_file), exist_ok=True)
        with open(self.used_verses_file, 'w', encoding='utf-8') as f:
            json.dump({
                'used_verse_ids': self.used_verses,
                'last_reset': datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
    
    def get_random_verse(self) -> Optional[Dict]:
        """الحصول على آية عشوائية غير مستخدمة"""
        available_verses = [
            verse for verse in self.verses_data['verses']
            if verse['id'] not in self.used_verses
        ]
        
        # إذا تم استخدام جميع الآيات، إعادة تعيين القائمة
        if not available_verses:
            self.reset_used_verses()
            available_verses = self.verses_data['verses']
        
        if available_verses:
            selected_verse = random.choice(available_verses)
            self.mark_verse_as_used(selected_verse['id'])
            return selected_verse
        
        return None
    
    def get_verse_by_theme(self, theme: str) -> Optional[Dict]:
        """الحصول على آية حسب الموضوع"""
        theme_verses = [
            verse for verse in self.verses_data['verses']
            if theme.lower() in verse.get('theme', '').lower()
            and verse['id'] not in self.used_verses
        ]
        
        if theme_verses:
            selected_verse = random.choice(theme_verses)
            self.mark_verse_as_used(selected_verse['id'])
            return selected_verse
        
        return None
    
    def mark_verse_as_used(self, verse_id: int):
        """تمييز آية كمستخدمة"""
        if verse_id not in self.used_verses:
            self.used_verses.append(verse_id)
            self._save_used_verses()
    
    def reset_used_verses(self):
        """إعادة تعيين قائمة الآيات المستخدمة"""
        self.used_verses = []
        self._save_used_verses()
        print("تم إعادة تعيين قائمة الآيات المستخدمة")
    
    def get_random_inspirational_phrase(self) -> str:
        """الحصول على عبارة إلهامية عشوائية"""
        phrases = self.verses_data.get('inspirational_phrases', [])
        return random.choice(phrases) if phrases else ""
    
    def get_verse_stats(self) -> Dict:
        """إحصائيات الآيات"""
        total_verses = len(self.verses_data['verses'])
        used_verses = len(self.used_verses)
        remaining_verses = total_verses - used_verses
        
        return {
            'total_verses': total_verses,
            'used_verses': used_verses,
            'remaining_verses': remaining_verses,
            'usage_percentage': (used_verses / total_verses * 100) if total_verses > 0 else 0
        }
    
    def add_new_verse(self, text: str, surah: str, verse_number: str, theme: str):
        """إضافة آية جديدة"""
        new_id = max([v['id'] for v in self.verses_data['verses']], default=0) + 1
        new_verse = {
            'id': new_id,
            'text': text,
            'surah': surah,
            'verse_number': verse_number,
            'theme': theme,
            'used': False
        }
        
        self.verses_data['verses'].append(new_verse)
        
        # حفظ البيانات المحدثة
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(self.verses_data, f, ensure_ascii=False, indent=2)
        
        return new_verse
