#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق مبسط لروبوت نشر الآيات القرآنية
"""

from flask import Flask, jsonify, render_template_string, request, redirect, url_for, session
import os
import logging
from datetime import datetime
import threading
import time

# استيراد الوحدات المخصصة
from config import get_config
from modules.quran_manager import QuranManager
from modules.image_generator import ImageGenerator

# محاولة استيراد المكونات الاختيارية
try:
    from modules.gemini_client import AdvancedGeminiClient
    GEMINI_AVAILABLE = True
except:
    GEMINI_AVAILABLE = False

try:
    from modules.telegram_publisher import TelegramPublisher
    TELEGRAM_AVAILABLE = True
except:
    TELEGRAM_AVAILABLE = False

# إعداد التطبيق
app = Flask(__name__)
config = get_config()
app.config.from_object(config)
app.secret_key = config.SECRET_KEY

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# المتغيرات العامة
quran_manager = None
image_generator = None
gemini_client = None
telegram_publisher = None

def initialize_components():
    """تهيئة المكونات الأساسية"""
    global quran_manager, image_generator, gemini_client, telegram_publisher
    
    try:
        # تهيئة مدير القرآن
        quran_manager = QuranManager()
        logger.info("تم تهيئة مدير القرآن بنجاح")
        
        # تهيئة مولد الصور
        image_generator = ImageGenerator()
        logger.info("تم تهيئة مولد الصور بنجاح")
        
        # تهيئة عميل Gemini إذا كان متوفراً
        if GEMINI_AVAILABLE and config.GEMINI_API_KEY:
            try:
                gemini_client = AdvancedGeminiClient()
                logger.info("تم تهيئة عميل Gemini بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة Gemini: {e}")
        
        # تهيئة ناشر Telegram إذا كان متوفراً
        if TELEGRAM_AVAILABLE and config.ENABLE_TELEGRAM and config.TELEGRAM_BOT_TOKEN:
            try:
                telegram_publisher = TelegramPublisher()
                logger.info("تم تهيئة ناشر Telegram بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة Telegram: {e}")
        
        logger.info("تم تهيئة المكونات الأساسية بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في تهيئة المكونات: {e}")
        return False

def publish_content():
    """نشر المحتوى"""
    try:
        logger.info("بدء عملية نشر المحتوى...")
        
        # الحصول على آية عشوائية
        verse = quran_manager.get_random_verse()
        if not verse:
            logger.error("لم يتم العثور على آية للنشر")
            return False
        
        # الحصول على عبارة إلهامية
        if gemini_client:
            try:
                phrase = gemini_client.get_enhanced_phrase()
            except:
                phrase = quran_manager.get_random_inspirational_phrase()
        else:
            phrase = quran_manager.get_random_inspirational_phrase()
        
        # توليد الصورة
        image_path = image_generator.generate_verse_image(verse, phrase)
        if not image_path or not os.path.exists(image_path):
            logger.error("فشل في توليد الصورة")
            return False
        
        # النشر على Telegram إذا كان متوفراً
        success = False
        if telegram_publisher:
            try:
                content = {
                    'verse': verse,
                    'phrase': phrase,
                    'caption': f"📖 {verse['surah']} - آية {verse['verse_number']}\n\n💭 {phrase}"
                }
                
                if telegram_publisher.publish_verse_content(image_path, content):
                    logger.info("تم النشر على Telegram بنجاح")
                    success = True
                else:
                    logger.error("فشل النشر على Telegram")
            except Exception as e:
                logger.error(f"خطأ في النشر على Telegram: {e}")
        
        # تنظيف الصورة المؤقتة
        try:
            if os.path.exists(image_path):
                os.remove(image_path)
        except Exception as e:
            logger.warning(f"فشل في حذف الصورة المؤقتة: {e}")
        
        return success
        
    except Exception as e:
        logger.error(f"خطأ في عملية النشر: {e}")
        return False

# الصفحة الرئيسية
@app.route('/')
def home():
    """الصفحة الرئيسية"""
    html_template = """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>روبوت نشر الآيات القرآنية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .container {
                max-width: 800px;
                margin: 50px auto;
                background: rgba(255, 255, 255, 0.95);
                padding: 40px;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 40px;
            }
            .header .icon {
                font-size: 4rem;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 15px;
            }
            .status-card {
                background: #f8f9fa;
                border-radius: 15px;
                padding: 25px;
                margin-bottom: 20px;
                border: 1px solid #e0e0e0;
            }
            .btn-custom {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                border-radius: 10px;
                padding: 12px 25px;
                color: white;
                font-weight: bold;
                margin: 5px;
                transition: all 0.3s ease;
            }
            .btn-custom:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                color: white;
            }
            .status-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                border-bottom: 1px solid #e0e0e0;
            }
            .status-item:last-child {
                border-bottom: none;
            }
            .status-success {
                color: #28a745;
            }
            .status-warning {
                color: #ffc107;
            }
            .status-error {
                color: #dc3545;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="icon">
                    <i class="fas fa-mosque"></i>
                </div>
                <h1>🕌 روبوت نشر الآيات القرآنية</h1>
                <p class="text-muted">نظام ذكي لنشر المحتوى الإسلامي</p>
            </div>
            
            <div class="status-card">
                <h3><i class="fas fa-info-circle me-2"></i>حالة النظام</h3>
                <div class="status-item">
                    <span>مدير القرآن</span>
                    <span class="status-success"><i class="fas fa-check-circle"></i> يعمل</span>
                </div>
                <div class="status-item">
                    <span>مولد الصور</span>
                    <span class="status-success"><i class="fas fa-check-circle"></i> يعمل</span>
                </div>
                <div class="status-item">
                    <span>Gemini AI</span>
                    <span class="{{ 'status-success' if gemini_available else 'status-warning' }}">
                        <i class="fas fa-{{ 'check-circle' if gemini_available else 'exclamation-triangle' }}"></i>
                        {{ 'يعمل' if gemini_available else 'غير متوفر' }}
                    </span>
                </div>
                <div class="status-item">
                    <span>Telegram</span>
                    <span class="{{ 'status-success' if telegram_available else 'status-warning' }}">
                        <i class="fas fa-{{ 'check-circle' if telegram_available else 'exclamation-triangle' }}"></i>
                        {{ 'يعمل' if telegram_available else 'غير متوفر' }}
                    </span>
                </div>
            </div>
            
            <div class="text-center">
                <button class="btn btn-custom" onclick="publishNow()">
                    <i class="fas fa-paper-plane me-2"></i>
                    نشر فوري
                </button>
                <button class="btn btn-custom" onclick="testSystem()">
                    <i class="fas fa-vial me-2"></i>
                    اختبار النظام
                </button>
                <button class="btn btn-custom" onclick="viewLogs()">
                    <i class="fas fa-list me-2"></i>
                    عرض السجلات
                </button>
            </div>
            
            <div id="result" class="mt-4"></div>
        </div>
        
        <script>
            async function publishNow() {
                document.getElementById('result').innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري النشر...</div>';
                
                try {
                    const response = await fetch('/publish', { method: 'POST' });
                    const data = await response.json();
                    
                    if (data.success) {
                        document.getElementById('result').innerHTML = '<div class="alert alert-success"><i class="fas fa-check me-2"></i>' + data.message + '</div>';
                    } else {
                        document.getElementById('result').innerHTML = '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>' + data.message + '</div>';
                    }
                } catch (error) {
                    document.getElementById('result').innerHTML = '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>خطأ في الاتصال</div>';
                }
            }
            
            async function testSystem() {
                document.getElementById('result').innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري اختبار النظام...</div>';
                
                try {
                    const response = await fetch('/test');
                    const data = await response.json();
                    
                    document.getElementById('result').innerHTML = '<div class="alert alert-info"><pre>' + data.result + '</pre></div>';
                } catch (error) {
                    document.getElementById('result').innerHTML = '<div class="alert alert-danger"><i class="fas fa-times me-2"></i>خطأ في الاختبار</div>';
                }
            }
            
            function viewLogs() {
                window.open('/logs', '_blank');
            }
        </script>
    </body>
    </html>
    """
    
    return render_template_string(html_template,
        gemini_available=gemini_client is not None,
        telegram_available=telegram_publisher is not None
    )

@app.route('/publish', methods=['POST'])
def api_publish():
    """نشر فوري"""
    try:
        success = publish_content()
        
        if success:
            return jsonify({'success': True, 'message': 'تم النشر بنجاح!'})
        else:
            return jsonify({'success': False, 'message': 'فشل في النشر'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/test')
def api_test():
    """اختبار النظام"""
    try:
        import subprocess
        result = subprocess.run(['python', 'test_app.py'], 
                              capture_output=True, text=True, timeout=30)
        
        return jsonify({'result': result.stdout})
    except Exception as e:
        return jsonify({'result': f'خطأ في الاختبار: {str(e)}'})

@app.route('/health')
def health_check():
    """فحص صحة التطبيق"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    print("🕌 بدء تشغيل روبوت نشر الآيات القرآنية")
    print("=" * 50)
    
    # تهيئة المكونات
    if initialize_components():
        print("✅ تم تهيئة المكونات بنجاح")
        print(f"🌐 التطبيق يعمل على: http://localhost:{config.PORT}")
        print("📱 افتح المتصفح واذهب للرابط أعلاه")
        print("=" * 50)
        
        # تشغيل التطبيق
        app.run(host='0.0.0.0', port=config.PORT, debug=False)
    else:
        print("❌ فشل في تهيئة التطبيق")
        print("🔧 راجع الإعدادات في ملف .env")
        exit(1)
