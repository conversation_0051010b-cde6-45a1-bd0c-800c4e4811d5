#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البوت المحدث مع النشر التلقائي
"""

import os
import sys
import logging
from datetime import datetime

def setup_logging():
    """إعداد نظام السجلات"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('bot_controller.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    # فحص ملف .env
    if not os.path.exists('.env'):
        print("❌ ملف .env غير موجود")
        print("💡 أنشئ ملف .env وأضف إعداداتك")
        return False
    
    # فحص الخط القرآني
    font_path = "fonts/Amiri Quran.ttf"
    if os.path.exists(font_path):
        print(f"✅ تم العثور على الخط القرآني: {font_path}")
    else:
        print(f"⚠ الخط القرآني غير موجود: {font_path}")
        print("💡 ضع الخط القرآني في مجلد fonts/")
    
    # فحص مجلدات البيانات
    required_dirs = ['data', 'generated_images', 'backgrounds']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)
            print(f"📁 تم إنشاء مجلد: {dir_name}")
        else:
            print(f"✅ مجلد موجود: {dir_name}")
    
    return True

def show_startup_info():
    """عرض معلومات البدء"""
    print("=" * 60)
    print("🤖 بوت ناشر الآيات القرآنية المحدث")
    print("=" * 60)
    print(f"📅 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    print("🎯 الميزات الجديدة:")
    print("  ✅ النشر التلقائي كل 3 ساعات (قابل للتخصيص)")
    print("  ✅ تشخيص مشاكل المنصات")
    print("  ✅ تحسينات في توليد الصور")
    print("  ✅ أوامر تحكم متقدمة")
    print()
    print("🎛 الأوامر الأساسية:")
    print("  /toggle_auto - تشغيل/إيقاف النشر التلقائي")
    print("  /auto_status - حالة النشر التلقائي")
    print("  /set_interval [ساعات] - تغيير فترة النشر")
    print("  /force_publish - نشر فوري")
    print("  /test_platforms - اختبار المنصات")
    print("  /diagnose_platforms - تشخيص المشاكل")
    print()
    print("=" * 60)

def main():
    """تشغيل البوت"""
    try:
        # إعداد السجلات
        setup_logging()
        
        # عرض معلومات البدء
        show_startup_info()
        
        # فحص المتطلبات
        if not check_requirements():
            print("❌ فشل في فحص المتطلبات")
            return False
        
        print("🚀 بدء تشغيل البوت...")
        
        # استيراد وتشغيل البوت
        from telegram_bot_controller import TelegramBotController
        
        # إنشاء البوت
        bot = TelegramBotController()
        
        print("✅ تم تهيئة البوت بنجاح")
        print("🔄 النشر التلقائي:", "مفعل" if bot.auto_publish_enabled else "معطل")
        print(f"⏱ فترة النشر: {bot.publish_interval // 3600} ساعات")
        print()
        print("🎯 البوت جاهز للاستخدام!")
        print("💡 استخدم /help في البوت لمعرفة جميع الأوامر")
        print("=" * 60)
        
        # تشغيل البوت
        bot.run()
        
    except KeyboardInterrupt:
        print("\n⏹ تم إيقاف البوت بواسطة المستخدم")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        logging.error(f"خطأ في تشغيل البوت: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 نصائح لحل المشاكل:")
        print("  1. تحقق من ملف .env")
        print("  2. تأكد من تثبيت المتطلبات")
        print("  3. راجع ملف bot_controller.log")
        print("  4. شغل python test_bot_improvements.py للاختبار")
        sys.exit(1)
    else:
        sys.exit(0)
