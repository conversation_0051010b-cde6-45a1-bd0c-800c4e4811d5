import os
import asyncio
import logging
import concurrent.futures
from telegram import Bo<PERSON>
from telegram.error import TelegramError, BadRequest, Forbidden, NetworkError
from typing import Optional, Dict

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class TelegramPublisher:
    """ناشر المحتوى على Telegram"""
    
    def __init__(self, bot_token: Optional[str] = None, channel_id: Optional[str] = None):
        self.bot_token = bot_token or os.getenv('TELEGRAM_BOT_TOKEN')
        self.channel_id = channel_id or os.getenv('TELEGRAM_CHANNEL_ID')

        if not self.bot_token:
            raise ValueError("رمز Telegram Bot مطلوب")
        if not self.channel_id:
            raise ValueError("معرف قناة Telegram مطلوب")

        # تنظيف معرف القناة من الرابط إذا كان موجوداً
        self.channel_id = self._extract_channel_username(self.channel_id)

        self.bot = Bot(token=self.bot_token)

    def _extract_channel_username(self, channel_input: str) -> str:
        """استخراج اسم المستخدم من رابط القناة أو المعرف"""
        if not channel_input:
            return channel_input

        # إزالة المسافات
        channel_input = channel_input.strip()

        # إذا كان رابط كامل
        if channel_input.startswith('https://t.me/'):
            username = channel_input.replace('https://t.me/', '')
            return f"@{username}" if not username.startswith('@') else username

        # إذا كان يبدأ بـ t.me/
        elif channel_input.startswith('t.me/'):
            username = channel_input.replace('t.me/', '')
            return f"@{username}" if not username.startswith('@') else username

        # إذا كان اسم مستخدم فقط بدون @
        elif not channel_input.startswith('@') and not channel_input.startswith('-'):
            return f"@{channel_input}"

        # إذا كان معرف صحيح بالفعل
        return channel_input
    
    async def send_image_with_caption(self, image_path: str, caption: str) -> bool:
        """إرسال صورة مع تعليق إلى القناة"""
        try:
            # التحقق من وجود الملف
            if not os.path.exists(image_path):
                logger.error(f"الصورة غير موجودة: {image_path}")
                return False

            # التحقق من حجم الملف (أقل من 10 ميجا)
            file_size = os.path.getsize(image_path)
            if file_size > 10 * 1024 * 1024:  # 10 MB
                logger.error(f"حجم الصورة كبير جداً: {file_size} بايت")
                return False

            with open(image_path, 'rb') as photo:
                message = await self.bot.send_photo(
                    chat_id=self.channel_id,
                    photo=photo,
                    caption=caption,
                    parse_mode='HTML'
                )

            logger.info(f"تم نشر الصورة بنجاح على Telegram: {image_path} (Message ID: {message.message_id})")
            return True

        except BadRequest as e:
            if "chat not found" in str(e).lower():
                logger.error(f"القناة غير موجودة أو البوت غير مضاف إليها: {self.channel_id}")
            elif "not enough rights" in str(e).lower():
                logger.error(f"البوت لا يملك صلاحيات النشر في القناة: {self.channel_id}")
            elif "file too large" in str(e).lower():
                logger.error(f"حجم الصورة كبير جداً: {image_path}")
            else:
                logger.error(f"خطأ في طلب Telegram: {e}")
            return False

        except Forbidden as e:
            logger.error(f"البوت محظور من النشر في القناة: {self.channel_id} - {e}")
            return False

        except NetworkError as e:
            logger.error(f"خطأ في الشبكة مع Telegram: {e}")
            return False

        except TelegramError as e:
            logger.error(f"خطأ في Telegram API: {e}")
            return False

        except FileNotFoundError:
            logger.error(f"الصورة غير موجودة: {image_path}")
            return False

        except Exception as e:
            logger.error(f"خطأ غير متوقع في Telegram: {e}")
            return False
    
    async def send_text_message(self, text: str) -> bool:
        """إرسال رسالة نصية إلى القناة"""
        try:
            await self.bot.send_message(
                chat_id=self.channel_id,
                text=text,
                parse_mode='HTML'
            )
            print("تم إرسال الرسالة النصية بنجاح على Telegram")
            return True
            
        except TelegramError as e:
            print(f"خطأ في إرسال الرسالة على Telegram: {e}")
            return False
        except Exception as e:
            print(f"خطأ غير متوقع في Telegram: {e}")
            return False
    
    def publish_verse_content(self, image_path: str, content: Dict) -> bool:
        """نشر محتوى الآية (صورة + تعليق)"""
        # تحضير التعليق
        verse_data = content['verse']
        phrase = content['phrase']
        caption = content.get('caption', '')

        # إنشاء تعليق افتراضي إذا لم يكن متوفراً
        if not caption:
            caption = f"""
🕌 <b>{phrase}</b>

📖 <i>"{verse_data['text']}"</i>

📚 سورة {verse_data['surah']} - آية {verse_data['verse_number']}

#قرآن #ذكر #دعاء #أمل #طمأنينة #إسلام
            """.strip()

        # تشغيل النشر بشكل متزامن مع معالجة event loop
        try:
            # التحقق من وجود event loop نشط
            try:
                loop = asyncio.get_running_loop()
                # إذا كان هناك loop نشط، استخدم create_task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(self._run_async_publish, image_path, caption)
                    return future.result()
            except RuntimeError:
                # لا يوجد loop نشط، يمكن إنشاء واحد جديد
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return loop.run_until_complete(
                        self.send_image_with_caption(image_path, caption)
                    )
                finally:
                    loop.close()
        except Exception as e:
            logger.error(f"خطأ في النشر: {e}")
            return False

    def _run_async_publish(self, image_path: str, caption: str) -> bool:
        """تشغيل النشر في loop منفصل"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(
                    self.send_image_with_caption(image_path, caption)
                )
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"خطأ في النشر المتزامن: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """اختبار الاتصال بـ Telegram"""
        try:
            # اختبار البوت
            bot_info = await self.bot.get_me()
            logger.info(f"تم الاتصال بنجاح مع البوت: {bot_info.username}")

            # اختبار القناة
            try:
                chat_info = await self.bot.get_chat(self.channel_id)
                logger.info(f"تم العثور على القناة: {chat_info.title}")

                # اختبار صلاحيات البوت
                try:
                    chat_member = await self.bot.get_chat_member(self.channel_id, bot_info.id)
                    if chat_member.status in ['administrator', 'creator']:
                        logger.info("البوت لديه صلاحيات إدارية في القناة")
                        return True
                    else:
                        logger.warning(f"البوت ليس مديراً في القناة. الحالة: {chat_member.status}")
                        return False
                except Exception as e:
                    logger.warning(f"لا يمكن التحقق من صلاحيات البوت: {e}")
                    return True  # نفترض أنه يعمل

            except BadRequest as e:
                if "chat not found" in str(e).lower():
                    logger.error(f"القناة غير موجودة: {self.channel_id}")
                else:
                    logger.error(f"خطأ في الوصول للقناة: {e}")
                return False

        except TelegramError as e:
            logger.error(f"خطأ في Telegram API: {e}")
            return False
        except Exception as e:
            logger.error(f"فشل في الاتصال مع Telegram: {e}")
            return False
    
    def format_verse_caption(self, verse_data: Dict, phrase: str, hashtags: list = None) -> str:
        """تنسيق تعليق الآية"""
        if hashtags is None:
            hashtags = ['#قرآن', '#ذكر', '#دعاء', '#أمل', '#طمأنينة', '#إسلام']
        
        caption = f"""
🕌 <b>{phrase}</b>

📖 <i>"{verse_data['text']}"</i>

📚 سورة {verse_data['surah']} - آية {verse_data['verse_number']}

{' '.join(hashtags)}
        """.strip()
        
        return caption

    async def diagnose_issues(self) -> Dict[str, any]:
        """تشخيص مشاكل النشر"""
        diagnosis = {
            'bot_token_valid': False,
            'channel_accessible': False,
            'bot_permissions': False,
            'issues': [],
            'recommendations': []
        }

        try:
            # فحص رمز البوت
            bot_info = await self.bot.get_me()
            diagnosis['bot_token_valid'] = True
            diagnosis['bot_info'] = {
                'username': bot_info.username,
                'first_name': bot_info.first_name,
                'id': bot_info.id
            }

        except Exception as e:
            diagnosis['issues'].append(f"رمز البوت غير صحيح: {e}")
            diagnosis['recommendations'].append("تحقق من رمز البوت في ملف .env")
            return diagnosis

        try:
            # فحص القناة
            chat_info = await self.bot.get_chat(self.channel_id)
            diagnosis['channel_accessible'] = True
            diagnosis['channel_info'] = {
                'title': chat_info.title,
                'type': chat_info.type,
                'id': chat_info.id,
                'username': getattr(chat_info, 'username', None)
            }

            # فحص صلاحيات البوت
            try:
                chat_member = await self.bot.get_chat_member(self.channel_id, bot_info.id)
                if chat_member.status in ['administrator', 'creator']:
                    diagnosis['bot_permissions'] = True
                    diagnosis['bot_status'] = chat_member.status
                else:
                    diagnosis['issues'].append(f"البوت ليس مديراً في القناة. الحالة: {chat_member.status}")
                    diagnosis['recommendations'].append("اجعل البوت مديراً في القناة مع صلاحية النشر")

            except Exception as e:
                diagnosis['issues'].append(f"لا يمكن التحقق من صلاحيات البوت: {e}")
                diagnosis['recommendations'].append("تأكد من أن البوت مضاف للقناة كمدير")

        except BadRequest as e:
            if "chat not found" in str(e).lower():
                diagnosis['issues'].append("القناة غير موجودة أو معرف القناة خاطئ")
                diagnosis['recommendations'].append("تحقق من معرف القناة في ملف .env")
            else:
                diagnosis['issues'].append(f"خطأ في الوصول للقناة: {e}")

        except Exception as e:
            diagnosis['issues'].append(f"خطأ في فحص القناة: {e}")

        return diagnosis
