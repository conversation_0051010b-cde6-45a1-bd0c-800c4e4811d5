import json
import os
import uuid
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import secrets

class UserManager:
    """مدير المستخدمين المتعددين مع إعدادات منفصلة"""
    
    def __init__(self, users_file: str = "data/users.json"):
        self.users_file = users_file
        self.users_data = self._load_users()
        self.active_sessions = {}  # جلسات المستخدمين النشطة
        
        # إنشاء مستخدم افتراضي إذا لم يوجد مستخدمون
        if not self.users_data.get('users'):
            self._create_default_user()
    
    def _load_users(self) -> Dict:
        """تحميل بيانات المستخدمين"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل بيانات المستخدمين: {e}")
        
        return {
            'users': {},
            'settings': {
                'max_users': 100,
                'session_timeout_hours': 24,
                'require_email_verification': False
            }
        }
    
    def _save_users(self):
        """حفظ بيانات المستخدمين"""
        os.makedirs(os.path.dirname(self.users_file), exist_ok=True)
        with open(self.users_file, 'w', encoding='utf-8') as f:
            json.dump(self.users_data, f, ensure_ascii=False, indent=2)
    
    def _create_default_user(self):
        """إنشاء مستخدم افتراضي"""
        default_user = {
            'id': 'default_user',
            'username': 'admin',
            'email': '<EMAIL>',
            'password_hash': self._hash_password('admin123'),
            'role': 'admin',
            'created_at': datetime.now().isoformat(),
            'is_active': True,
            'settings': self._get_default_user_settings(),
            'usage_stats': {
                'posts_created': 0,
                'last_login': None,
                'total_logins': 0
            }
        }
        
        self.users_data['users']['default_user'] = default_user
        self._save_users()
        print("تم إنشاء مستخدم افتراضي: admin / admin123")
    
    def _get_default_user_settings(self) -> Dict:
        """الإعدادات الافتراضية للمستخدم"""
        return {
            'platforms': {
                'telegram': {
                    'enabled': True,
                    'bot_token': '',
                    'channel_id': '',
                    'custom_hashtags': ['#قرآن', '#ذكر', '#دعاء']
                },
                'instagram': {
                    'enabled': True,
                    'username': '',
                    'password': '',
                    'custom_hashtags': ['#قرآن', '#إسلام', '#روحانيات']
                },
                'facebook': {
                    'enabled': True,
                    'access_token': '',
                    'page_id': '',
                    'custom_hashtags': ['#قرآن', '#إيمان', '#تذكير']
                }
            },
            'content': {
                'publish_interval_hours': 4,
                'custom_verses': [],
                'custom_phrases': [],
                'preferred_themes': ['الصبر', 'الأمل', 'الرزق', 'التوبة'],
                'language': 'ar',
                'include_tafseer': False
            },
            'design': {
                'template_style': 'classic',
                'font_family': 'default',
                'color_scheme': 'blue_gradient',
                'image_size': '1080x1080',
                'watermark': {
                    'enabled': False,
                    'text': '',
                    'position': 'bottom_right'
                }
            },
            'ai': {
                'gemini_api_key': '',
                'creativity_level': 'medium',
                'generate_custom_phrases': True,
                'auto_suggest_themes': True
            },
            'notifications': {
                'email_reports': False,
                'success_notifications': True,
                'error_notifications': True
            }
        }
    
    def _hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            salt, stored_hash = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac('sha256',
                                                    password.encode('utf-8'),
                                                    salt.encode('utf-8'),
                                                    100000)
            return password_hash_check.hex() == stored_hash
        except:
            return False
    
    def create_user(self, username: str, email: str, password: str, role: str = 'user') -> Dict:
        """إنشاء مستخدم جديد"""
        # التحقق من عدم وجود المستخدم
        if self.get_user_by_username(username):
            raise ValueError(f"اسم المستخدم '{username}' موجود بالفعل")
        
        if self.get_user_by_email(email):
            raise ValueError(f"البريد الإلكتروني '{email}' مستخدم بالفعل")
        
        # التحقق من الحد الأقصى للمستخدمين
        max_users = self.users_data['settings']['max_users']
        if len(self.users_data['users']) >= max_users:
            raise ValueError(f"تم الوصول للحد الأقصى من المستخدمين ({max_users})")
        
        # إنشاء المستخدم
        user_id = str(uuid.uuid4())
        user_data = {
            'id': user_id,
            'username': username,
            'email': email,
            'password_hash': self._hash_password(password),
            'role': role,
            'created_at': datetime.now().isoformat(),
            'is_active': True,
            'settings': self._get_default_user_settings(),
            'usage_stats': {
                'posts_created': 0,
                'last_login': None,
                'total_logins': 0
            }
        }
        
        self.users_data['users'][user_id] = user_data
        self._save_users()
        
        return {'user_id': user_id, 'username': username, 'email': email}
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """تسجيل دخول المستخدم"""
        user = self.get_user_by_username(username)
        if not user:
            return None
        
        if not user['is_active']:
            return None
        
        if not self._verify_password(password, user['password_hash']):
            return None
        
        # تحديث إحصائيات تسجيل الدخول
        user['usage_stats']['last_login'] = datetime.now().isoformat()
        user['usage_stats']['total_logins'] += 1
        self._save_users()
        
        # إنشاء جلسة
        session_token = secrets.token_urlsafe(32)
        session_data = {
            'user_id': user['id'],
            'username': user['username'],
            'role': user['role'],
            'created_at': datetime.now(),
            'expires_at': datetime.now() + timedelta(hours=self.users_data['settings']['session_timeout_hours'])
        }
        
        self.active_sessions[session_token] = session_data
        
        return {
            'user_id': user['id'],
            'username': user['username'],
            'role': user['role'],
            'session_token': session_token
        }
    
    def get_user_by_session(self, session_token: str) -> Optional[Dict]:
        """الحصول على المستخدم من رمز الجلسة"""
        session = self.active_sessions.get(session_token)
        if not session:
            return None
        
        # التحقق من انتهاء صلاحية الجلسة
        if datetime.now() > session['expires_at']:
            del self.active_sessions[session_token]
            return None
        
        return self.get_user_by_id(session['user_id'])
    
    def get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """الحصول على المستخدم بالمعرف"""
        return self.users_data['users'].get(user_id)
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """الحصول على المستخدم باسم المستخدم"""
        for user in self.users_data['users'].values():
            if user['username'] == username:
                return user
        return None
    
    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """الحصول على المستخدم بالبريد الإلكتروني"""
        for user in self.users_data['users'].values():
            if user['email'] == email:
                return user
        return None
    
    def update_user_settings(self, user_id: str, settings: Dict) -> bool:
        """تحديث إعدادات المستخدم"""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        # دمج الإعدادات الجديدة مع الموجودة
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(user['settings'], settings)
        self._save_users()
        return True
    
    def get_user_settings(self, user_id: str) -> Optional[Dict]:
        """الحصول على إعدادات المستخدم"""
        user = self.get_user_by_id(user_id)
        return user['settings'] if user else None
    
    def increment_user_posts(self, user_id: str):
        """زيادة عداد منشورات المستخدم"""
        user = self.get_user_by_id(user_id)
        if user:
            user['usage_stats']['posts_created'] += 1
            self._save_users()
    
    def get_all_users(self) -> List[Dict]:
        """الحصول على جميع المستخدمين (للمشرفين فقط)"""
        users = []
        for user in self.users_data['users'].values():
            user_info = {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'role': user['role'],
                'created_at': user['created_at'],
                'is_active': user['is_active'],
                'posts_created': user['usage_stats']['posts_created'],
                'last_login': user['usage_stats']['last_login']
            }
            users.append(user_info)
        return users
    
    def deactivate_user(self, user_id: str) -> bool:
        """إلغاء تفعيل المستخدم"""
        user = self.get_user_by_id(user_id)
        if user:
            user['is_active'] = False
            self._save_users()
            return True
        return False
    
    def logout_user(self, session_token: str) -> bool:
        """تسجيل خروج المستخدم"""
        if session_token in self.active_sessions:
            del self.active_sessions[session_token]
            return True
        return False
