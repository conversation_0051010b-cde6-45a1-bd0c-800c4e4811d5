#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الإعدادات المتقدم
يوفر واجهة سهلة لإدارة وتعديل إعدادات التطبيق
"""

import os
import sys
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

# إعداد الترميز للعربية
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

logger = logging.getLogger(__name__)

class ConfigManager:
    """مدير الإعدادات المتقدم"""
    
    def __init__(self, env_file_path: str = '.env'):
        self.env_file_path = env_file_path
        self.config_history_file = 'data/config_history.json'
        self._ensure_data_directory()
    
    def _ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        os.makedirs('data', exist_ok=True)
    
    def get_current_config(self) -> Dict[str, Any]:
        """الحصول على الإعدادات الحالية"""
        config = {}
        
        if os.path.exists(self.env_file_path):
            with open(self.env_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip()
        
        return config
    
    def update_config_value(self, key: str, value: str, user_id: Optional[int] = None) -> bool:
        """تحديث قيمة في الإعدادات"""
        try:
            # حفظ النسخة الاحتياطية
            self._backup_config(user_id, key, value)
            
            # قراءة الملف الحالي
            lines = []
            if os.path.exists(self.env_file_path):
                with open(self.env_file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            
            # البحث عن المفتاح وتحديثه أو إضافته
            key_found = False
            for i, line in enumerate(lines):
                if line.strip().startswith(f"{key}="):
                    lines[i] = f"{key}={value}\n"
                    key_found = True
                    break
            
            # إضافة المفتاح إذا لم يوجد
            if not key_found:
                lines.append(f"{key}={value}\n")
            
            # كتابة الملف المحدث
            with open(self.env_file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            logger.info(f"تم تحديث الإعداد {key} بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث الإعداد {key}: {e}")
            return False
    
    def _backup_config(self, user_id: Optional[int], key: str, new_value: str):
        """إنشاء نسخة احتياطية من التغيير"""
        try:
            current_config = self.get_current_config()
            old_value = current_config.get(key, '')
            
            backup_entry = {
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'key': key,
                'old_value': old_value,
                'new_value': new_value
            }
            
            # قراءة التاريخ الحالي
            history = []
            if os.path.exists(self.config_history_file):
                with open(self.config_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # إضافة الإدخال الجديد
            history.append(backup_entry)
            
            # الاحتفاظ بآخر 100 تغيير فقط
            if len(history) > 100:
                history = history[-100:]
            
            # حفظ التاريخ المحدث
            with open(self.config_history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
    
    def get_config_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """الحصول على تاريخ التغييرات"""
        try:
            if os.path.exists(self.config_history_file):
                with open(self.config_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                return history[-limit:] if history else []
            return []
        except Exception as e:
            logger.error(f"خطأ في قراءة تاريخ الإعدادات: {e}")
            return []
    
    def validate_config(self) -> Dict[str, List[str]]:
        """التحقق من صحة الإعدادات"""
        config = self.get_current_config()
        validation_results = {
            'errors': [],
            'warnings': [],
            'info': []
        }
        
        # التحقق من إعدادات Telegram
        if config.get('ENABLE_TELEGRAM', '').lower() == 'true':
            if not config.get('TELEGRAM_BOT_TOKEN'):
                validation_results['errors'].append('رمز بوت Telegram مطلوب عند تفعيل Telegram')
            if not config.get('TELEGRAM_CHANNEL_ID'):
                validation_results['errors'].append('معرف قناة Telegram مطلوب عند تفعيل Telegram')
        
        # التحقق من إعدادات Instagram
        if config.get('ENABLE_INSTAGRAM', '').lower() == 'true':
            if not config.get('INSTAGRAM_USERNAME'):
                validation_results['errors'].append('اسم مستخدم Instagram مطلوب عند تفعيل Instagram')
            if not config.get('INSTAGRAM_PASSWORD'):
                validation_results['errors'].append('كلمة مرور Instagram مطلوبة عند تفعيل Instagram')
        
        # التحقق من إعدادات Facebook
        if config.get('ENABLE_FACEBOOK', '').lower() == 'true':
            if not config.get('FACEBOOK_ACCESS_TOKEN'):
                validation_results['errors'].append('رمز وصول Facebook مطلوب عند تفعيل Facebook')
            if not config.get('FACEBOOK_PAGE_ID'):
                validation_results['errors'].append('معرف صفحة Facebook مطلوب عند تفعيل Facebook')
        
        # التحقق من Gemini API
        if not config.get('GEMINI_API_KEY'):
            validation_results['warnings'].append('مفتاح Gemini API غير محدد - ستستخدم العبارات الافتراضية')
        
        # التحقق من تفعيل منصة واحدة على الأقل
        platforms_enabled = any([
            config.get('ENABLE_TELEGRAM', '').lower() == 'true',
            config.get('ENABLE_INSTAGRAM', '').lower() == 'true',
            config.get('ENABLE_FACEBOOK', '').lower() == 'true'
        ])
        
        if not platforms_enabled:
            validation_results['warnings'].append('لا توجد منصات مفعلة - لن يتم النشر')
        
        return validation_results
    
    def get_config_summary(self) -> str:
        """الحصول على ملخص الإعدادات"""
        config = self.get_current_config()
        validation = self.validate_config()
        
        summary = "📋 ملخص الإعدادات الحالية:\n\n"
        
        # المنصات المفعلة
        summary += "📱 المنصات:\n"
        platforms = []
        if config.get('ENABLE_TELEGRAM', '').lower() == 'true':
            platforms.append('Telegram ✅')
        else:
            platforms.append('Telegram ❌')
            
        if config.get('ENABLE_INSTAGRAM', '').lower() == 'true':
            platforms.append('Instagram ✅')
        else:
            platforms.append('Instagram ❌')
            
        if config.get('ENABLE_FACEBOOK', '').lower() == 'true':
            platforms.append('Facebook ✅')
        else:
            platforms.append('Facebook ❌')
        
        summary += f"• {' | '.join(platforms)}\n\n"
        
        # إعدادات أخرى
        summary += "⚙️ إعدادات أخرى:\n"
        summary += f"• فترة النشر: {config.get('PUBLISH_INTERVAL_HOURS', '4')} ساعات\n"
        summary += f"• Gemini AI: {'✅ مفعل' if config.get('GEMINI_API_KEY') else '❌ معطل'}\n"
        summary += f"• أبعاد الصورة: {config.get('IMAGE_WIDTH', '1080')}x{config.get('IMAGE_HEIGHT', '1080')}\n\n"
        
        # نتائج التحقق
        if validation['errors']:
            summary += "❌ أخطاء:\n"
            for error in validation['errors']:
                summary += f"• {error}\n"
            summary += "\n"
        
        if validation['warnings']:
            summary += "⚠️ تحذيرات:\n"
            for warning in validation['warnings']:
                summary += f"• {warning}\n"
            summary += "\n"
        
        if not validation['errors'] and not validation['warnings']:
            summary += "✅ جميع الإعدادات صحيحة!\n"
        
        return summary
    
    def export_config(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """تصدير الإعدادات"""
        config = self.get_current_config()
        
        if not include_sensitive:
            # إخفاء البيانات الحساسة
            sensitive_keys = [
                'TELEGRAM_BOT_TOKEN', 'INSTAGRAM_PASSWORD', 
                'FACEBOOK_ACCESS_TOKEN', 'GEMINI_API_KEY'
            ]
            
            for key in sensitive_keys:
                if key in config and config[key]:
                    config[key] = '***HIDDEN***'
        
        return {
            'config': config,
            'exported_at': datetime.now().isoformat(),
            'validation': self.validate_config()
        }
