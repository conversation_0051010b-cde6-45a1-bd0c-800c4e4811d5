#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي شامل للمميزات المتقدمة
"""

import os
import sys
import time
from datetime import datetime

def demo_user_management():
    """عرض توضيحي لإدارة المستخدمين"""
    print("👥 عرض توضيحي لإدارة المستخدمين")
    print("=" * 40)
    
    try:
        from modules.user_manager import UserManager
        
        user_manager = UserManager()
        print("✅ تم تهيئة مدير المستخدمين")
        
        # عرض المستخدمين الموجودين
        users = user_manager.get_all_users()
        print(f"📊 عدد المستخدمين الحاليين: {len(users)}")
        
        for user in users[:3]:  # عرض أول 3 مستخدمين
            print(f"   - {user['username']} ({user['role']}) - {user['posts_created']} منشور")
        
        # اختبار إنشاء مستخدم جديد
        try:
            test_user = user_manager.create_user(
                f"demo_user_{int(time.time())}", 
                f"demo_{int(time.time())}@example.com", 
                "DemoPass123!"
            )
            print(f"✅ تم إنشاء مستخدم تجريبي: {test_user['username']}")
        except ValueError as e:
            print(f"⚠️ {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض إدارة المستخدمين: {e}")
        return False

def demo_template_system():
    """عرض توضيحي لنظام القوالب"""
    print("\n🎨 عرض توضيحي لنظام القوالب")
    print("=" * 40)
    
    try:
        from modules.template_manager import TemplateManager
        
        template_manager = TemplateManager()
        print("✅ تم تهيئة مدير القوالب")
        
        # عرض القوالب المتاحة
        templates = template_manager.get_template_list()
        print(f"📋 القوالب المتاحة ({len(templates)}):")
        for template in templates:
            print(f"   - {template['name']}: {template['description']}")
        
        # عرض مخططات الألوان
        color_schemes = template_manager.get_color_schemes_list()
        print(f"\n🎨 مخططات الألوان ({len(color_schemes)}):")
        for scheme in color_schemes:
            print(f"   - {scheme['name']}")
        
        # إنشاء خلفية تجريبية
        print("\n🖼️ إنشاء خلفية تجريبية...")
        background = template_manager.create_background_with_template(
            'modern', 'blue_gradient'
        )
        
        # حفظ الخلفية
        demo_bg_path = "generated_images/demo_template_background.png"
        os.makedirs(os.path.dirname(demo_bg_path), exist_ok=True)
        background.save(demo_bg_path)
        print(f"✅ تم حفظ خلفية تجريبية: {demo_bg_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض نظام القوالب: {e}")
        return False

def demo_analytics_system():
    """عرض توضيحي لنظام التحليلات"""
    print("\n📊 عرض توضيحي لنظام التحليلات")
    print("=" * 40)
    
    try:
        from modules.analytics_manager import AnalyticsManager
        
        analytics_manager = AnalyticsManager()
        print("✅ تم تهيئة مدير التحليلات")
        
        # إضافة بيانات تجريبية
        demo_posts = [
            {
                'id': 1,
                'verse_text': 'وَعَسَىٰ أَن تَكْرَهُوا شَيْئًا وَهُوَ خَيْرٌ لَّكُمْ',
                'surah': 'البقرة',
                'verse_number': '216',
                'phrase': 'اللهم عوضني بالأجمل',
                'theme': 'الصبر',
                'platforms': ['telegram', 'instagram'],
                'success_platforms': ['telegram', 'instagram'],
                'failed_platforms': [],
                'template_used': 'modern',
                'color_scheme': 'blue_gradient',
                'ai_generated': True
            },
            {
                'id': 2,
                'verse_text': 'فَإِنَّ مَعَ الْعُسْرِ يُسْرًا',
                'surah': 'الشرح',
                'verse_number': '5',
                'phrase': 'ربي أرني الفرح بمستقبلي',
                'theme': 'الأمل',
                'platforms': ['telegram', 'facebook'],
                'success_platforms': ['telegram'],
                'failed_platforms': ['facebook'],
                'template_used': 'classic',
                'color_scheme': 'green_nature',
                'ai_generated': False
            }
        ]
        
        for post in demo_posts:
            analytics_manager.record_post(post)
            print(f"📝 تم تسجيل منشور: {post['surah']} - آية {post['verse_number']}")
        
        # عرض ملخص الأداء
        summary = analytics_manager.get_performance_summary(30)
        if 'error' not in summary:
            print(f"\n📈 ملخص الأداء (30 يوم):")
            print(f"   - إجمالي المنشورات: {summary['total_posts']}")
            print(f"   - معدل النجاح: {summary['success_rate']}%")
            print(f"   - متوسط الأداء: {summary['avg_performance_score']}")
        
        # عرض رؤى المحتوى
        insights = analytics_manager.get_content_insights()
        if 'error' not in insights:
            print(f"\n💡 رؤى المحتوى:")
            if insights.get('theme_insights'):
                print("   المواضيع الأكثر نجاحاً:")
                for theme, data in list(insights['theme_insights'].items())[:3]:
                    print(f"     - {theme}: {data['avg_performance']} نقطة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض نظام التحليلات: {e}")
        return False

def demo_security_system():
    """عرض توضيحي لنظام الأمان"""
    print("\n🔒 عرض توضيحي لنظام الأمان")
    print("=" * 40)
    
    try:
        from modules.security_manager import SecurityManager
        
        security_manager = SecurityManager()
        print("✅ تم تهيئة مدير الأمان")
        
        # اختبار قوة كلمة المرور
        test_passwords = [
            "123456",
            "password",
            "MyPass123",
            "MySecurePass123!@#"
        ]
        
        print("\n🔐 اختبار قوة كلمات المرور:")
        for password in test_passwords:
            result = security_manager.validate_password_strength(password)
            print(f"   '{password}': {result['strength']} ({result['score']}/100)")
        
        # اختبار توليد رمز آمن
        token = security_manager.generate_secure_token("demo_user", {"test": True})
        print(f"\n🎫 تم توليد رمز آمن: {token[:20]}...")
        
        # التحقق من الرمز
        validation = security_manager.validate_token(token)
        if validation:
            print("✅ تم التحقق من الرمز بنجاح")
        
        # عرض تقرير الأمان
        security_report = security_manager.get_security_report()
        print(f"\n📋 تقرير الأمان:")
        print(f"   - محاولات تسجيل دخول فاشلة (24س): {security_report['failed_login_attempts_24h']}")
        print(f"   - الجلسات النشطة: {security_report['active_sessions']}")
        print(f"   - عناوين IP محظورة: {security_report['blocked_ips']}")
        
        if security_report['recommendations']:
            print("   التوصيات الأمنية:")
            for rec in security_report['recommendations'][:2]:
                print(f"     - {rec}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض نظام الأمان: {e}")
        return False

def demo_unique_features():
    """عرض توضيحي للمميزات الفريدة"""
    print("\n🌟 عرض توضيحي للمميزات الفريدة")
    print("=" * 40)
    
    try:
        from modules.unique_features import UniqueFeatures, SmartTimingEngine, MoodDetector
        
        # التوقيت الذكي
        timing_engine = SmartTimingEngine()
        optimal_time = timing_engine.get_optimal_posting_time()
        print(f"⏰ التوقيت الذكي:")
        print(f"   - الفترة الحالية: {optimal_time['current_period']}")
        print(f"   - المزاج المناسب: {optimal_time['mood']}")
        print(f"   - المواضيع المقترحة: {', '.join(optimal_time['recommended_themes'][:2])}")
        
        # كاشف المزاج
        mood_detector = MoodDetector()
        current_mood = mood_detector.detect_mood_from_time()
        mood_content = mood_detector.get_mood_based_content(current_mood)
        print(f"\n😊 كاشف المزاج:")
        print(f"   - المزاج الحالي: {current_mood}")
        print(f"   - المواضيع المناسبة: {', '.join(mood_content['recommended_themes'][:2])}")
        print(f"   - الألوان المقترحة: {', '.join(mood_content['color_palette'][:2])}")
        
        print("✅ تم عرض المميزات الفريدة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض المميزات الفريدة: {e}")
        return False

def main():
    """تشغيل العرض التوضيحي الشامل"""
    print("🚀 عرض توضيحي شامل للمميزات المتقدمة")
    print("=" * 60)
    print(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    demos = [
        ("إدارة المستخدمين", demo_user_management),
        ("نظام القوالب", demo_template_system),
        ("نظام التحليلات", demo_analytics_system),
        ("نظام الأمان", demo_security_system),
        ("المميزات الفريدة", demo_unique_features)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        try:
            result = demo_func()
            results[demo_name] = result
            time.sleep(1)  # توقف قصير بين العروض
        except Exception as e:
            print(f"❌ خطأ في عرض {demo_name}: {e}")
            results[demo_name] = False
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج العرض التوضيحي:")
    print("=" * 60)
    
    successful_demos = 0
    for demo_name, result in results.items():
        if result:
            print(f"✅ {demo_name}: نجح")
            successful_demos += 1
        else:
            print(f"❌ {demo_name}: فشل")
    
    print(f"\n🎉 نجح {successful_demos}/{len(demos)} عرض توضيحي")
    
    if successful_demos == len(demos):
        print("🌟 جميع المميزات المتقدمة تعمل بنجاح!")
        print("🚀 الأداة جاهزة للاستخدام مع جميع المميزات المتقدمة")
    else:
        print("🔧 بعض المميزات تحتاج لمراجعة")
    
    print(f"\n⏰ وقت الانتهاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
