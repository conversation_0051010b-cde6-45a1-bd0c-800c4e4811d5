# 🤖 دليل التحكم عبر بوت Telegram

## 🎯 نظرة عامة

تم تطوير النسخة الجديدة من روبوت نشر الآيات القرآنية لتكون **مفتوحة المصدر بالكامل** مع **تحكم كامل عبر بوت Telegram** بدون الحاجة لواجهة ويب.

## ✅ التطبيق جاهز للاستخدام!

### 🚀 كيفية البدء

1. **تأكد من تشغيل التطبيق**:
   ```bash
   python main.py
   ```

2. **ابحث عن البوت في Telegram**:
   - افتح Telegram
   - ابحث عن البوت باستخدام الرمز المميز الخاص بك
   - أو استخدم الرابط المباشر إذا كان متوفراً

3. **ابدأ المحادثة**:
   - اضغط "Start" أو أرسل `/start`
   - ستظهر لك القائمة الرئيسية مع الأزرار

## 📋 الأوامر المتاحة

### 🔧 الأوامر الأساسية

| الأمر | الوصف | الاستخدام |
|-------|--------|-----------|
| `/start` | بدء البوت وعرض القائمة الرئيسية | `/start` |
| `/status` | عرض حالة النظام والمكونات | `/status` |
| `/publish` | نشر آية قرآنية فوراً | `/publish` |
| `/settings` | عرض وتعديل الإعدادات | `/settings` |
| `/stats` | عرض الإحصائيات المفصلة | `/stats` |
| `/help` | عرض المساعدة | `/help` |

### ⚙️ أوامر الإعدادات

| الأمر | الوصف | الاستخدام |
|-------|--------|-----------|
| `/toggle_auto` | تشغيل/إيقاف النشر التلقائي | `/toggle_auto` |
| `/set_interval` | تغيير فترة النشر التلقائي | `/set_interval 6` |

### 👥 أوامر الإدارة

| الأمر | الوصف | الاستخدام |
|-------|--------|-----------|
| `/add_user` | إضافة مستخدم مصرح له | `/add_user 123456789` |
| `/remove_user` | إزالة مستخدم | `/remove_user 123456789` |
| `/list_users` | عرض المستخدمين المصرح لهم | `/list_users` |

## 🎮 الأزرار التفاعلية

### 🏠 القائمة الرئيسية
عند إرسال `/start` ستظهر الأزرار التالية:

- **📊 الحالة**: عرض حالة النظام والمكونات
- **⚡ نشر فوري**: نشر آية قرآنية فوراً
- **⚙️ الإعدادات**: عرض وتعديل الإعدادات
- **📈 الإحصائيات**: عرض الإحصائيات المفصلة
- **🔄 تشغيل/إيقاف التلقائي**: تبديل حالة النشر التلقائي
- **❓ المساعدة**: عرض المساعدة

### 📊 صفحة الحالة
تعرض معلومات مفصلة عن:
- حالة المكونات (مدير القرآن، مولد الصور، Gemini AI، ناشر Telegram)
- حالة النشر التلقائي
- آخر وقت نشر
- التوقيت المتوقع للنشر التالي

### ⚡ النشر الفوري
عند الضغط على "نشر فوري":
1. يتم اختيار آية قرآنية عشوائية
2. توليد عبارة إلهامية بالذكاء الاصطناعي
3. إنشاء صورة جميلة للآية
4. النشر على قناة Telegram
5. عرض تفاصيل المنشور

## 🔧 الإعدادات المتاحة

### ⏰ فترة النشر التلقائي
- **الافتراضي**: كل 4 ساعات
- **النطاق المسموح**: 1-24 ساعة
- **التغيير**: استخدم `/set_interval <ساعات>`

### 🤖 النشر التلقائي
- **التشغيل/الإيقاف**: استخدم `/toggle_auto` أو الزر المخصص
- **المراقبة**: يتم فحص الحالة كل دقيقة
- **التنفيذ**: ينشر تلقائياً حسب الفترة المحددة

### 👥 إدارة المستخدمين
- **إضافة مستخدم**: `/add_user <user_id>`
- **إزالة مستخدم**: `/remove_user <user_id>`
- **عرض القائمة**: `/list_users`
- **ملاحظة**: إذا كانت القائمة فارغة، يمكن لأي شخص استخدام البوت

## 📈 الإحصائيات والمراقبة

### 📊 الإحصائيات المتاحة
- آخر وقت نشر
- حالة النشر التلقائي
- إجمالي الآيات المتاحة
- عدد الآيات المستخدمة
- حالة جميع المكونات

### 📝 السجلات
- **ملف السجل الرئيسي**: `quran_bot.log`
- **ملف سجل البوت**: `bot_controller.log`
- **المحتوى**: جميع العمليات والأخطاء مسجلة

## 🎨 ما يتم نشره

### 📖 المحتوى
كل منشور يحتوي على:
- **آية قرآنية** من القرآن الكريم
- **اسم السورة ورقم الآية**
- **عبارة إلهامية** مولدة بالذكاء الاصطناعي
- **صورة جميلة** مصممة تلقائياً

### 🖼️ تصميم الصورة
- **الأبعاد**: 1080x1080 بكسل
- **الخلفيات**: متنوعة وجميلة
- **الخطوط**: عربية أنيقة
- **التخطيط**: توزيع تلقائي للنص

### 📱 مثال على المنشور
```
📖 البقرة - آية 216

وَعَسَىٰ أَن تَكْرَهُوا شَيْئًا وَهُوَ خَيْرٌ لَّكُمْ

💭 اللهم عوضني بالأجمل يا رب
```

## 🛠️ استكشاف الأخطاء

### ❌ البوت لا يرد
**الأسباب المحتملة**:
- رمز البوت غير صحيح
- انقطاع الإنترنت
- التطبيق متوقف

**الحلول**:
1. تحقق من رمز البوت في ملف `.env`
2. تأكد من تشغيل التطبيق: `python main.py`
3. راجع ملف السجلات

### ❌ فشل النشر
**الأسباب المحتملة**:
- البوت غير مضاف كمشرف في القناة
- معرف القناة غير صحيح
- مشكلة في صلاحيات البوت

**الحلول**:
1. تأكد من إضافة البوت كمشرف في القناة
2. تحقق من معرف القناة في ملف `.env`
3. تأكد من صلاحيات النشر للبوت

### ❌ مشاكل الخطوط العربية
**الأسباب المحتملة**:
- خطوط عربية غير متوفرة
- مشكلة في ترميز النص

**الحلول**:
1. تأكد من وجود خطوط عربية في مجلد `fonts/`
2. تحقق من إعدادات الترميز في النظام

## 💡 نصائح للاستخدام الأمثل

### 🎯 للمستخدمين الجدد
1. **ابدأ بالأوامر الأساسية**: `/start`, `/status`, `/publish`
2. **جرب النشر الفوري** للتأكد من عمل النظام
3. **راقب الإحصائيات** لفهم أداء النظام
4. **استخدم المساعدة** عند الحاجة: `/help`

### 🔧 للمستخدمين المتقدمين
1. **خصص فترة النشر** حسب جمهورك
2. **أضف مستخدمين مصرح لهم** للأمان
3. **راقب السجلات** لتتبع الأداء
4. **استخدم الإحصائيات** لتحسين التوقيت

### 📊 لمراقبة الأداء
1. **تحقق من الحالة** بانتظام
2. **راجع الإحصائيات** أسبوعياً
3. **تابع السجلات** للأخطاء
4. **اختبر النشر الفوري** دورياً

## 🎉 الخلاصة

**النسخة الجديدة من روبوت نشر الآيات القرآنية تقدم:**

✅ **تحكم كامل عبر Telegram** - لا حاجة لواجهة ويب  
✅ **مفتوح المصدر بالكامل** - مجاني للجميع  
✅ **سهل الاستخدام** - أوامر بسيطة وأزرار تفاعلية  
✅ **دعم كامل للعربية** - بدون مشاكل ترميز  
✅ **أداء محسن** - موارد أقل واستقرار أكثر  

**ابدأ الآن واستمتع بنشر كلام الله بأجمل صورة! 🕌**
