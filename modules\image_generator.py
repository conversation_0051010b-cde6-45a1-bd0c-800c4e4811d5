import os
import random
from PIL import Image, ImageDraw, ImageFont
from typing import Tuple, Optional
import arabic_reshaper
from bidi.algorithm import get_display
import textwrap

class ImageGenerator:
    """مولد الصور مع الآيات القرآنية والخلفيات"""
    
    def __init__(self,
                 backgrounds_dir: str = "backgrounds",
                 fonts_dir: str = "fonts",
                 output_dir: str = "generated_images"):
        self.backgrounds_dir = backgrounds_dir
        self.fonts_dir = fonts_dir
        self.output_dir = output_dir

        # إنشاء مجلد الإخراج إذا لم يكن موجوداً
        os.makedirs(output_dir, exist_ok=True)

        # التحقق من وجود الخط القرآني
        self._check_quran_font()

        # الألوان المتاحة للنص
        self.text_colors = [
            (255, 255, 255),  # أبيض
            (240, 240, 240),  # أبيض مائل للرمادي
            (255, 215, 0),    # ذهبي
            (255, 255, 224),  # أبيض كريمي
            (245, 245, 220),  # بيج فاتح
        ]

        # أحجام الخط
        self.font_sizes = {
            'verse': 48,      # حجم خط الآية
            'surah': 32,      # حجم خط اسم السورة
            'phrase': 36      # حجم خط العبارة الإلهامية
        }

    def _check_quran_font(self):
        """التحقق من وجود الخط القرآني وطباعة معلومات مفيدة"""
        quran_font_path = os.path.join(self.fonts_dir, "Amiri Quran.ttf")
        if os.path.exists(quran_font_path):
            print(f"✓ تم العثور على الخط القرآني: {quran_font_path}")
        else:
            print(f"⚠ لم يتم العثور على الخط القرآني في: {quran_font_path}")
            if os.path.exists(self.fonts_dir):
                available_fonts = [f for f in os.listdir(self.fonts_dir)
                                 if f.lower().endswith(('.ttf', '.otf'))]
                if available_fonts:
                    print(f"الخطوط المتوفرة: {', '.join(available_fonts)}")
                else:
                    print("لا توجد خطوط متوفرة في مجلد الخطوط")
            else:
                print(f"مجلد الخطوط غير موجود: {self.fonts_dir}")
    
    def get_random_background(self) -> Optional[str]:
        """الحصول على خلفية عشوائية"""
        if not os.path.exists(self.backgrounds_dir):
            return None
        
        backgrounds = [f for f in os.listdir(self.backgrounds_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        if backgrounds:
            return os.path.join(self.backgrounds_dir, random.choice(backgrounds))
        return None
    
    def get_quran_font(self) -> Optional[str]:
        """الحصول على الخط القرآني المحدد"""
        # البحث عن خط Amiri Quran أولاً
        quran_font_path = os.path.join(self.fonts_dir, "Amiri Quran.ttf")
        if os.path.exists(quran_font_path):
            return quran_font_path

        # البحث عن أي خط قرآني آخر
        if not os.path.exists(self.fonts_dir):
            return None

        fonts = [f for f in os.listdir(self.fonts_dir)
                if f.lower().endswith(('.ttf', '.otf'))]

        # إعطاء أولوية للخطوط القرآنية
        quran_fonts = [f for f in fonts if any(keyword in f.lower()
                      for keyword in ['quran', 'قرآن', 'amiri', 'uthmanic'])]

        if quran_fonts:
            return os.path.join(self.fonts_dir, quran_fonts[0])
        elif fonts:
            return os.path.join(self.fonts_dir, fonts[0])
        return None
    
    def create_default_background(self, size: Tuple[int, int] = (1080, 1080)) -> Image.Image:
        """إنشاء خلفية افتراضية بتدرج لوني"""
        img = Image.new('RGB', size, color=(20, 30, 50))
        draw = ImageDraw.Draw(img)
        
        # إنشاء تدرج لوني بسيط
        for y in range(size[1]):
            color_ratio = y / size[1]
            r = int(20 + (60 - 20) * color_ratio)
            g = int(30 + (80 - 30) * color_ratio)
            b = int(50 + (120 - 50) * color_ratio)
            draw.line([(0, y), (size[0], y)], fill=(r, g, b))
        
        return img
    
    def prepare_arabic_text(self, text: str) -> str:
        """تحضير النص العربي للعرض الصحيح"""
        try:
            # تنظيف النص من الأحرف غير المرغوب فيها
            cleaned_text = text.strip()

            # طباعة النص الأصلي للتشخيص
            print(f"🔍 النص الأصلي: '{cleaned_text}'")
            print(f"📏 طول النص الأصلي: {len(cleaned_text)}")

            # فحص وجود أحرف عربية في النص الأصلي
            original_arabic_chars = [c for c in cleaned_text if '\u0600' <= c <= '\u06FF']
            print(f"🔤 عدد الأحرف العربية في النص الأصلي: {len(original_arabic_chars)}")

            # إعادة تشكيل النص العربي مع معاملات محسنة
            # استخدام المعاملات المتوفرة في الإصدار الحالي
            reshaped_text = arabic_reshaper.reshape(cleaned_text)

            print(f"🔄 النص بعد إعادة التشكيل: '{reshaped_text}'")
            print(f"📏 طول النص بعد إعادة التشكيل: {len(reshaped_text)}")

            # تطبيق خوارزمية BiDi للعرض الصحيح
            bidi_text = get_display(reshaped_text)

            print(f"✅ النص النهائي: '{bidi_text}'")
            print(f"📏 طول النص النهائي: {len(bidi_text)}")

            # التحقق من صحة النص المحضر
            if len(bidi_text) == 0:
                print("⚠️ تحذير: النص المحضر فارغ، سيتم استخدام النص الأصلي")
                return text

            # فحص وجود أحرف عربية في النص النهائي (نطاق أوسع)
            # النطاق الأساسي للعربية: U+0600-U+06FF
            # النطاق الإضافي للعربية: U+0750-U+077F, U+FB50-U+FDFF, U+FE70-U+FEFF
            final_arabic_chars = [c for c in bidi_text if (
                '\u0600' <= c <= '\u06FF' or  # النطاق الأساسي
                '\u0750' <= c <= '\u077F' or  # النطاق الإضافي
                '\uFB50' <= c <= '\uFDFF' or  # الأشكال العربية المقدمة A
                '\uFE70' <= c <= '\uFEFF'     # الأشكال العربية المقدمة B
            )]
            print(f"🔤 عدد الأحرف العربية في النص النهائي: {len(final_arabic_chars)}")

            # إذا لم نجد أحرف عربية في النطاقات المعتادة، نتحقق من وجود أي أحرف غير لاتينية
            if len(final_arabic_chars) == 0:
                non_latin_chars = [c for c in bidi_text if ord(c) > 127]
                print(f"🔤 عدد الأحرف غير اللاتينية: {len(non_latin_chars)}")

                if len(non_latin_chars) > 0:
                    print("✅ النص يحتوي على أحرف غير لاتينية (ربما عربية محولة)")
                else:
                    print("⚠️ تحذير: لا توجد أحرف عربية في النص المحضر")
                    # محاولة إرجاع النص الأصلي إذا كان يحتوي على أحرف عربية
                    if len(original_arabic_chars) > 0:
                        print("🔄 إرجاع النص الأصلي لأنه يحتوي على أحرف عربية")
                        return cleaned_text

            return bidi_text
        except Exception as e:
            print(f"خطأ في تحضير النص العربي: {e}")
            # محاولة بسيطة بدون معاملات إضافية
            try:
                reshaped_text = arabic_reshaper.reshape(text)
                return get_display(reshaped_text)
            except Exception as e2:
                print(f"خطأ في المحاولة البديلة: {e2}")
                return text  # إرجاع النص الأصلي في حالة الخطأ
    
    def wrap_arabic_text(self, text: str, font: ImageFont.ImageFont, max_width: int) -> list:
        """تقسيم النص العربي إلى أسطر متعددة"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = f"{current_line} {word}".strip()
            bbox = font.getbbox(self.prepare_arabic_text(test_line))
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def get_text_position(self, img_size: Tuple[int, int], text_size: Tuple[int, int], 
                         position: str = "center") -> Tuple[int, int]:
        """حساب موضع النص"""
        img_width, img_height = img_size
        text_width, text_height = text_size
        
        if position == "center":
            x = (img_width - text_width) // 2
            y = (img_height - text_height) // 2
        elif position == "top":
            x = (img_width - text_width) // 2
            y = img_height // 4
        elif position == "bottom":
            x = (img_width - text_width) // 2
            y = img_height - img_height // 4 - text_height
        else:
            x = (img_width - text_width) // 2
            y = (img_height - text_height) // 2
        
        return (x, y)
    
    def add_text_shadow(self, draw: ImageDraw.Draw, text: str, position: Tuple[int, int], 
                       font: ImageFont.ImageFont, text_color: Tuple[int, int, int]):
        """إضافة ظل للنص"""
        shadow_color = (0, 0, 0, 128)  # أسود شفاف
        shadow_offset = 3
        
        # رسم الظل
        shadow_pos = (position[0] + shadow_offset, position[1] + shadow_offset)
        draw.text(shadow_pos, text, font=font, fill=shadow_color)
        
        # رسم النص الأساسي
        draw.text(position, text, font=font, fill=text_color)
    
    def generate_verse_image(self, verse_data: dict, inspirational_phrase: str = "") -> str:
        """توليد صورة للآية القرآنية"""
        # الحصول على خلفية
        background_path = self.get_random_background()
        
        if background_path and os.path.exists(background_path):
            try:
                img = Image.open(background_path)
                img = img.resize((1080, 1080), Image.Resampling.LANCZOS)
            except Exception as e:
                print(f"خطأ في تحميل الخلفية: {e}")
                img = self.create_default_background()
        else:
            img = self.create_default_background()
        
        draw = ImageDraw.Draw(img)
        
        # الحصول على الخط القرآني
        font_path = self.get_quran_font()

        try:
            if font_path and os.path.exists(font_path):
                print(f"🎨 استخدام الخط القرآني: {font_path}")

                # تحميل الخط مع أحجام مختلفة
                verse_font = ImageFont.truetype(font_path, self.font_sizes['verse'])
                surah_font = ImageFont.truetype(font_path, self.font_sizes['surah'])
                phrase_font = ImageFont.truetype(font_path, self.font_sizes['phrase'])

                # اختبار الخط مع نص عربي بسيط
                test_text = "بسم الله"
                test_bbox = verse_font.getbbox(test_text)
                test_width = test_bbox[2] - test_bbox[0]
                print(f"🧪 اختبار الخط: النص='{test_text}', العرض={test_width}")

                if test_width <= 0:
                    print("⚠️ تحذير: الخط لا يدعم النص العربي بشكل صحيح")
                else:
                    print("✅ الخط يدعم النص العربي بنجاح")

            else:
                print("⚠️ تحذير: لم يتم العثور على خط قرآني، سيتم استخدام خط افتراضي")
                # محاولة استخدام خطوط نظام تدعم العربية
                arabic_fonts = [
                    "arial.ttf",           # Arial
                    "tahoma.ttf",          # Tahoma
                    "calibri.ttf",         # Calibri
                    "segoeui.ttf",         # Segoe UI
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/tahoma.ttf"
                ]

                font_loaded = False
                for font_name in arabic_fonts:
                    try:
                        verse_font = ImageFont.truetype(font_name, self.font_sizes['verse'])
                        surah_font = ImageFont.truetype(font_name, self.font_sizes['surah'])
                        phrase_font = ImageFont.truetype(font_name, self.font_sizes['phrase'])
                        print(f"✅ تم تحميل خط بديل: {font_name}")
                        font_loaded = True
                        break
                    except:
                        continue

                if not font_loaded:
                    print("⚠️ لم يتم العثور على خط يدعم العربية، استخدام الخط الافتراضي")
                    verse_font = ImageFont.load_default()
                    surah_font = ImageFont.load_default()
                    phrase_font = ImageFont.load_default()
        except Exception as e:
            print(f"خطأ في تحميل الخط: {e}")
            try:
                # محاولة أخيرة باستخدام خط نظام
                verse_font = ImageFont.truetype("arial.ttf", self.font_sizes['verse'])
                surah_font = ImageFont.truetype("arial.ttf", self.font_sizes['surah'])
                phrase_font = ImageFont.truetype("arial.ttf", self.font_sizes['phrase'])
            except:
                verse_font = ImageFont.load_default()
                surah_font = ImageFont.load_default()
                phrase_font = ImageFont.load_default()
        
        # تحضير النصوص
        verse_text = verse_data['text']
        surah_info = f"سورة {verse_data['surah']} - آية {verse_data['verse_number']}"
        
        # تقسيم النص إلى أسطر
        max_width = img.width - 100  # هامش 50 بكسل من كل جانب
        verse_lines = self.wrap_arabic_text(verse_text, verse_font, max_width)
        
        # حساب الارتفاع الإجمالي للنص
        line_height = verse_font.getbbox("أ")[3] - verse_font.getbbox("أ")[1] + 10
        total_verse_height = len(verse_lines) * line_height
        
        surah_height = surah_font.getbbox("أ")[3] - surah_font.getbbox("أ")[1]
        phrase_height = phrase_font.getbbox("أ")[3] - phrase_font.getbbox("أ")[1] if inspirational_phrase else 0
        
        total_height = total_verse_height + surah_height + phrase_height + 60  # مسافات إضافية
        
        # موضع البداية
        start_y = (img.height - total_height) // 2
        
        # لون النص
        text_color = random.choice(self.text_colors)
        
        # رسم الآية
        current_y = start_y
        for line in verse_lines:
            prepared_line = self.prepare_arabic_text(line)
            bbox = verse_font.getbbox(prepared_line)
            text_width = bbox[2] - bbox[0]
            x = (img.width - text_width) // 2
            
            self.add_text_shadow(draw, prepared_line, (x, current_y), verse_font, text_color)
            current_y += line_height
        
        # رسم معلومات السورة
        current_y += 30
        prepared_surah = self.prepare_arabic_text(surah_info)
        bbox = surah_font.getbbox(prepared_surah)
        text_width = bbox[2] - bbox[0]
        x = (img.width - text_width) // 2
        
        self.add_text_shadow(draw, prepared_surah, (x, current_y), surah_font, text_color)
        
        # رسم العبارة الإلهامية إذا كانت موجودة
        if inspirational_phrase:
            current_y += surah_height + 30
            prepared_phrase = self.prepare_arabic_text(inspirational_phrase)
            bbox = phrase_font.getbbox(prepared_phrase)
            text_width = bbox[2] - bbox[0]
            x = (img.width - text_width) // 2
            
            # لون مختلف للعبارة الإلهامية
            phrase_color = (255, 215, 0)  # ذهبي
            self.add_text_shadow(draw, prepared_phrase, (x, current_y), phrase_font, phrase_color)
        
        # حفظ الصورة
        timestamp = int(os.path.getmtime(__file__) if os.path.exists(__file__) else 0)
        filename = f"verse_{verse_data['id']}_{timestamp}.png"
        output_path = os.path.join(self.output_dir, filename)
        
        img.save(output_path, "PNG", quality=95)
        return output_path
