#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لتوليد صورة واحدة
"""

import os
from modules.image_generator import ImageGenerator

def quick_test():
    """اختبار سريع لتوليد صورة"""
    
    print("🖼️ اختبار سريع لتوليد صورة")
    print("=" * 40)
    
    # إنشاء مولد الصور
    image_generator = ImageGenerator()
    
    # بيانات آية تجريبية
    verse_data = {
        'id': 999,
        'text': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        'surah': 'الفاتحة',
        'verse_number': 1
    }
    
    # عبارة إلهامية
    phrase = 'اللّهُمّ بَارِكْ لَنَا فِيمَا رَزَقْتَنَا'
    
    print(f"📖 الآية: {verse_data['text']}")
    print(f"💭 العبارة: {phrase}")
    
    print("\n🎨 بدء توليد الصورة...")
    
    try:
        image_path = image_generator.generate_verse_image(verse_data, phrase)
        
        if image_path and os.path.exists(image_path):
            print(f"\n✅ تم إنشاء الصورة: {image_path}")
            file_size = os.path.getsize(image_path)
            print(f"📊 حجم الملف: {file_size:,} بايت")
            return True
        else:
            print("\n❌ فشل في إنشاء الصورة")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n🎉 الاختبار نجح! تحقق من الصورة في مجلد generated_images")
    else:
        print("\n⚠️ الاختبار فشل")
