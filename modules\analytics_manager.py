import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import statistics
from collections import defaultdict, Counter

class AnalyticsManager:
    """مدير التحليلات والإحصائيات المتقدم"""
    
    def __init__(self, user_id: str = "default"):
        self.user_id = user_id
        self.analytics_file = f"data/analytics_{user_id}.json"
        self.analytics_data = self._load_analytics()
    
    def _load_analytics(self) -> Dict:
        """تحميل بيانات التحليلات"""
        try:
            if os.path.exists(self.analytics_file):
                with open(self.analytics_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل التحليلات: {e}")
        
        return {
            'posts': [],
            'performance_metrics': {},
            'user_engagement': {},
            'content_analysis': {},
            'platform_stats': {},
            'time_analysis': {},
            'created_at': datetime.now().isoformat()
        }
    
    def _save_analytics(self):
        """حفظ بيانات التحليلات"""
        os.makedirs(os.path.dirname(self.analytics_file), exist_ok=True)
        with open(self.analytics_file, 'w', encoding='utf-8') as f:
            json.dump(self.analytics_data, f, ensure_ascii=False, indent=2)
    
    def record_post(self, post_data: Dict):
        """تسجيل منشور جديد"""
        post_record = {
            'id': post_data.get('id', len(self.analytics_data['posts']) + 1),
            'timestamp': datetime.now().isoformat(),
            'verse_id': post_data.get('verse_id'),
            'verse_text': post_data.get('verse_text', ''),
            'surah': post_data.get('surah', ''),
            'verse_number': post_data.get('verse_number', ''),
            'phrase': post_data.get('phrase', ''),
            'theme': post_data.get('theme', ''),
            'platforms': post_data.get('platforms', []),
            'template_used': post_data.get('template_used', 'default'),
            'color_scheme': post_data.get('color_scheme', 'default'),
            'ai_generated': post_data.get('ai_generated', False),
            'success_platforms': post_data.get('success_platforms', []),
            'failed_platforms': post_data.get('failed_platforms', []),
            'engagement': {
                'telegram': {'views': 0, 'reactions': 0, 'shares': 0},
                'instagram': {'likes': 0, 'comments': 0, 'shares': 0, 'saves': 0},
                'facebook': {'likes': 0, 'comments': 0, 'shares': 0, 'reactions': 0}
            },
            'performance_score': 0.0
        }
        
        self.analytics_data['posts'].append(post_record)
        self._update_aggregated_stats(post_record)
        self._save_analytics()
    
    def update_post_engagement(self, post_id: int, platform: str, engagement_data: Dict):
        """تحديث بيانات التفاعل للمنشور"""
        for post in self.analytics_data['posts']:
            if post['id'] == post_id:
                if platform in post['engagement']:
                    post['engagement'][platform].update(engagement_data)
                    post['performance_score'] = self._calculate_performance_score(post)
                    self._save_analytics()
                    return True
        return False
    
    def _calculate_performance_score(self, post: Dict) -> float:
        """حساب نقاط الأداء للمنشور"""
        total_score = 0.0
        platform_count = 0
        
        for platform, engagement in post['engagement'].items():
            if platform in post['success_platforms']:
                platform_count += 1
                
                if platform == 'telegram':
                    score = (engagement.get('views', 0) * 0.1 + 
                            engagement.get('reactions', 0) * 2 + 
                            engagement.get('shares', 0) * 5)
                elif platform == 'instagram':
                    score = (engagement.get('likes', 0) * 1 + 
                            engagement.get('comments', 0) * 3 + 
                            engagement.get('shares', 0) * 5 + 
                            engagement.get('saves', 0) * 4)
                elif platform == 'facebook':
                    score = (engagement.get('likes', 0) * 1 + 
                            engagement.get('comments', 0) * 3 + 
                            engagement.get('shares', 0) * 5 + 
                            engagement.get('reactions', 0) * 2)
                else:
                    score = 0
                
                total_score += score
        
        return total_score / max(platform_count, 1)
    
    def _update_aggregated_stats(self, post: Dict):
        """تحديث الإحصائيات المجمعة"""
        # إحصائيات المنصات
        for platform in post['success_platforms']:
            if platform not in self.analytics_data['platform_stats']:
                self.analytics_data['platform_stats'][platform] = {
                    'total_posts': 0,
                    'success_rate': 0.0,
                    'avg_performance': 0.0
                }
            self.analytics_data['platform_stats'][platform]['total_posts'] += 1
        
        # إحصائيات المحتوى
        theme = post.get('theme', 'غير محدد')
        if 'themes' not in self.analytics_data['content_analysis']:
            self.analytics_data['content_analysis']['themes'] = {}
        
        if theme not in self.analytics_data['content_analysis']['themes']:
            self.analytics_data['content_analysis']['themes'][theme] = {
                'count': 0,
                'avg_performance': 0.0,
                'best_performing_post': None
            }
        
        self.analytics_data['content_analysis']['themes'][theme]['count'] += 1
        
        # إحصائيات الوقت
        post_time = datetime.fromisoformat(post['timestamp'])
        hour = post_time.hour
        day_of_week = post_time.weekday()
        
        if 'posting_times' not in self.analytics_data['time_analysis']:
            self.analytics_data['time_analysis']['posting_times'] = {
                'hours': defaultdict(int),
                'days_of_week': defaultdict(int)
            }
        
        self.analytics_data['time_analysis']['posting_times']['hours'][str(hour)] += 1
        self.analytics_data['time_analysis']['posting_times']['days_of_week'][str(day_of_week)] += 1
    
    def get_performance_summary(self, days: int = 30) -> Dict:
        """الحصول على ملخص الأداء"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_posts = [
            post for post in self.analytics_data['posts']
            if datetime.fromisoformat(post['timestamp']) >= cutoff_date
        ]
        
        if not recent_posts:
            return {'error': 'لا توجد منشورات في الفترة المحددة'}
        
        # حساب الإحصائيات
        total_posts = len(recent_posts)
        successful_posts = len([p for p in recent_posts if p['success_platforms']])
        success_rate = (successful_posts / total_posts) * 100 if total_posts > 0 else 0
        
        performance_scores = [p['performance_score'] for p in recent_posts if p['performance_score'] > 0]
        avg_performance = statistics.mean(performance_scores) if performance_scores else 0
        
        # أفضل المنشورات
        best_posts = sorted(recent_posts, key=lambda x: x['performance_score'], reverse=True)[:5]
        
        # إحصائيات المنصات
        platform_performance = {}
        for platform in ['telegram', 'instagram', 'facebook']:
            platform_posts = [p for p in recent_posts if platform in p['success_platforms']]
            if platform_posts:
                platform_scores = [p['performance_score'] for p in platform_posts]
                platform_performance[platform] = {
                    'posts_count': len(platform_posts),
                    'avg_performance': statistics.mean(platform_scores),
                    'success_rate': (len(platform_posts) / total_posts) * 100
                }
        
        return {
            'period_days': days,
            'total_posts': total_posts,
            'successful_posts': successful_posts,
            'success_rate': round(success_rate, 2),
            'avg_performance_score': round(avg_performance, 2),
            'best_performing_posts': [
                {
                    'id': post['id'],
                    'verse': f"{post['surah']} - آية {post['verse_number']}",
                    'performance_score': round(post['performance_score'], 2),
                    'timestamp': post['timestamp']
                }
                for post in best_posts
            ],
            'platform_performance': platform_performance
        }
    
    def get_content_insights(self) -> Dict:
        """الحصول على رؤى المحتوى"""
        posts = self.analytics_data['posts']
        
        if not posts:
            return {'error': 'لا توجد منشورات للتحليل'}
        
        # تحليل المواضيع
        theme_performance = {}
        for post in posts:
            theme = post.get('theme', 'غير محدد')
            if theme not in theme_performance:
                theme_performance[theme] = []
            theme_performance[theme].append(post['performance_score'])
        
        theme_insights = {}
        for theme, scores in theme_performance.items():
            if scores:
                theme_insights[theme] = {
                    'posts_count': len(scores),
                    'avg_performance': round(statistics.mean(scores), 2),
                    'best_performance': round(max(scores), 2),
                    'consistency': round(1 - (statistics.stdev(scores) / statistics.mean(scores)), 2) if len(scores) > 1 and statistics.mean(scores) > 0 else 0
                }
        
        # تحليل القوالب
        template_performance = defaultdict(list)
        for post in posts:
            template = post.get('template_used', 'default')
            template_performance[template].append(post['performance_score'])
        
        template_insights = {}
        for template, scores in template_performance.items():
            if scores:
                template_insights[template] = {
                    'posts_count': len(scores),
                    'avg_performance': round(statistics.mean(scores), 2)
                }
        
        # تحليل استخدام AI
        ai_posts = [p for p in posts if p.get('ai_generated', False)]
        manual_posts = [p for p in posts if not p.get('ai_generated', False)]
        
        ai_vs_manual = {
            'ai_generated': {
                'count': len(ai_posts),
                'avg_performance': round(statistics.mean([p['performance_score'] for p in ai_posts]), 2) if ai_posts else 0
            },
            'manual': {
                'count': len(manual_posts),
                'avg_performance': round(statistics.mean([p['performance_score'] for p in manual_posts]), 2) if manual_posts else 0
            }
        }
        
        return {
            'theme_insights': theme_insights,
            'template_insights': template_insights,
            'ai_vs_manual': ai_vs_manual,
            'total_posts_analyzed': len(posts)
        }
    
    def get_optimal_posting_times(self) -> Dict:
        """الحصول على أفضل أوقات النشر"""
        posts = self.analytics_data['posts']
        
        if not posts:
            return {'error': 'لا توجد بيانات كافية'}
        
        # تحليل الأداء حسب الساعة
        hour_performance = defaultdict(list)
        day_performance = defaultdict(list)
        
        for post in posts:
            post_time = datetime.fromisoformat(post['timestamp'])
            hour = post_time.hour
            day = post_time.weekday()
            
            hour_performance[hour].append(post['performance_score'])
            day_performance[day].append(post['performance_score'])
        
        # أفضل الساعات
        best_hours = []
        for hour, scores in hour_performance.items():
            if len(scores) >= 3:  # على الأقل 3 منشورات
                avg_score = statistics.mean(scores)
                best_hours.append({
                    'hour': hour,
                    'avg_performance': round(avg_score, 2),
                    'posts_count': len(scores)
                })
        
        best_hours.sort(key=lambda x: x['avg_performance'], reverse=True)
        
        # أفضل أيام الأسبوع
        days_names = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
        best_days = []
        for day, scores in day_performance.items():
            if len(scores) >= 2:  # على الأقل منشورين
                avg_score = statistics.mean(scores)
                best_days.append({
                    'day': days_names[day],
                    'day_number': day,
                    'avg_performance': round(avg_score, 2),
                    'posts_count': len(scores)
                })
        
        best_days.sort(key=lambda x: x['avg_performance'], reverse=True)
        
        return {
            'best_hours': best_hours[:5],  # أفضل 5 ساعات
            'best_days': best_days[:3],    # أفضل 3 أيام
            'recommendations': self._generate_posting_recommendations(best_hours, best_days)
        }
    
    def _generate_posting_recommendations(self, best_hours: List, best_days: List) -> List[str]:
        """توليد توصيات النشر"""
        recommendations = []
        
        if best_hours:
            top_hour = best_hours[0]
            recommendations.append(f"أفضل وقت للنشر هو الساعة {top_hour['hour']}:00 بمتوسط أداء {top_hour['avg_performance']}")
        
        if best_days:
            top_day = best_days[0]
            recommendations.append(f"أفضل يوم للنشر هو {top_day['day']} بمتوسط أداء {top_day['avg_performance']}")
        
        if len(best_hours) >= 2:
            recommendations.append(f"الساعات المفضلة للنشر: {', '.join([str(h['hour']) + ':00' for h in best_hours[:3]])}")
        
        return recommendations
    
    def export_analytics_report(self, format_type: str = "json") -> str:
        """تصدير تقرير التحليلات"""
        report_data = {
            'generated_at': datetime.now().isoformat(),
            'user_id': self.user_id,
            'summary': self.get_performance_summary(),
            'content_insights': self.get_content_insights(),
            'optimal_times': self.get_optimal_posting_times(),
            'raw_data': {
                'total_posts': len(self.analytics_data['posts']),
                'platform_stats': self.analytics_data.get('platform_stats', {}),
                'content_analysis': self.analytics_data.get('content_analysis', {})
            }
        }
        
        if format_type == "json":
            report_file = f"reports/analytics_report_{self.user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(report_file), exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            return report_file
        
        return json.dumps(report_data, ensure_ascii=False, indent=2)
