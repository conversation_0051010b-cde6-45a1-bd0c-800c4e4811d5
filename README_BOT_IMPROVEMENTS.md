# 🤖 دليل البوت المحدث - ناشر الآيات القرآنية المستقل

## 🎯 الميزات الجديدة

### ✅ النشر التلقائي المستقل
- **النشر كل 3 ساعات افتراضياً** (قابل للتخصيص من 1-24 ساعة)
- **حفظ تلقائي للحالة** - يتذكر البوت إعداداتك حتى بعد إعادة التشغيل
- **تحكم كامل من داخل البوت** - لا حاجة لواجهة ويب منفصلة

### ✅ تشخيص وحل مشاكل النشر
- **تشخيص تلقائي للمنصات** - يكتشف مشاكل Telegram وInstagram وFacebook
- **نصائح لحل المشاكل** - يقدم حلول عملية لكل مشكلة
- **اختبار سريع للاتصال** - تحقق من حالة جميع المنصات بأمر واحد

### ✅ تحسينات في توليد الصور
- **استخدام تلقائي للخط القرآني** - يستخدم "Amiri Quran.ttf" تلقائياً
- **معالجة محسنة للنص العربي** - عرض صحيح للحروف والتشكيل
- **فحص جودة الصور** - يتأكد من صحة الصورة قبل النشر

### ✅ أوامر تحكم متقدمة
- **أوامر جديدة للتحكم السريع**
- **واجهة أزرار تفاعلية**
- **إعداد سريع للفترات الزمنية**

## 🚀 كيفية الاستخدام

### 1. تشغيل البوت
```bash
python telegram_bot_controller.py
```

### 2. الأوامر الأساسية

#### 🎛 التحكم في النشر التلقائي
- `/toggle_auto` - تشغيل/إيقاف النشر التلقائي
- `/set_interval [ساعات]` - تغيير فترة النشر (1-24 ساعة)
- `/auto_status` - عرض حالة النشر التلقائي المفصلة
- `/quick_setup` - إعداد سريع لفترة النشر

#### ⚡ النشر الفوري
- `/publish` - نشر آية واحدة فوراً
- `/force_publish` - نشر فوري مع تحديث وقت آخر نشر

#### 🔧 تشخيص المشاكل
- `/test_platforms` - اختبار اتصال جميع المنصات
- `/diagnose_platforms` - تشخيص مفصل لمشاكل المنصات
- `/status` - حالة النظام العامة

#### 📊 المعلومات والإحصائيات
- `/start` - القائمة الرئيسية
- `/help` - دليل الأوامر الكامل
- `/settings` - عرض الإعدادات الحالية

### 3. أمثلة عملية

#### تفعيل النشر كل 3 ساعات:
```
/toggle_auto
/set_interval 3
```

#### تشخيص مشاكل النشر:
```
/test_platforms
/diagnose_platforms
```

#### نشر فوري:
```
/force_publish
```

## 🛠 إعداد المنصات

### Telegram
1. أنشئ بوت جديد عبر [@BotFather](https://t.me/BotFather)
2. احصل على رمز البوت
3. أضف البوت كمدير في قناتك
4. احصل على معرف القناة

### Instagram
1. استخدم حساب Instagram عادي (ليس تجاري)
2. تأكد من عدم تفعيل المصادقة الثنائية
3. استخدم كلمة مرور قوية

### Facebook
1. أنشئ تطبيق Facebook
2. احصل على Access Token
3. احصل على معرف الصفحة

## 📁 ملف .env المطلوب

```env
# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHANNEL_ID=@your_channel_username

# Instagram (اختياري)
INSTAGRAM_USERNAME=your_username
INSTAGRAM_PASSWORD=your_password

# Facebook (اختياري)
FACEBOOK_ACCESS_TOKEN=your_access_token
FACEBOOK_PAGE_ID=your_page_id

# Gemini AI (اختياري)
GEMINI_API_KEY=your_gemini_api_key

# إعدادات عامة
PUBLISH_INTERVAL_HOURS=3
ENABLE_TELEGRAM=true
ENABLE_INSTAGRAM=false
ENABLE_FACEBOOK=false
```

## 🔍 حل المشاكل الشائعة

### مشكلة: "البوت لا يملك صلاحيات"
**الحل:**
1. تأكد من أن البوت مضاف كمدير في القناة
2. امنح البوت صلاحية "Post Messages"
3. استخدم `/diagnose_platforms` للتشخيص

### مشكلة: "فشل في تسجيل الدخول لـ Instagram"
**الحل:**
1. تعطيل المصادقة الثنائية
2. استخدام كلمة مرور تطبيق إذا لزم الأمر
3. تجنب تسجيل الدخول من أجهزة متعددة

### مشكلة: "الحروف العربية تظهر كرموز"
**الحل:**
1. تأكد من وجود الخط القرآني في `fonts/Amiri Quran.ttf`
2. البوت يستخدم الخط تلقائياً الآن

### مشكلة: "النشر التلقائي لا يعمل"
**الحل:**
1. استخدم `/toggle_auto` للتفعيل
2. تحقق من `/auto_status` لمعرفة الحالة
3. تأكد من إعداد المنصات بشكل صحيح

## 📊 مراقبة الأداء

### عرض حالة النشر التلقائي:
```
/auto_status
```
يعرض:
- حالة التفعيل (مفعل/معطل)
- فترة النشر الحالية
- الوقت المتبقي للنشر التالي
- آخر وقت نشر
- المنصات المفعلة

### اختبار المنصات:
```
/test_platforms
```
يعرض حالة الاتصال لكل منصة:
- ✅ متصل
- ❌ غير متصل
- ⚠️ غير مُعد

## 🎯 نصائح للاستخدام الأمثل

### 1. فترات النشر المقترحة:
- **كل 3 ساعات**: مناسب للقنوات النشطة
- **كل 6 ساعات**: متوازن للمعظم
- **كل 12 ساعة**: للقنوات الهادئة
- **كل 24 ساعة**: منشور يومي واحد

### 2. أفضل الممارسات:
- استخدم `/test_platforms` يومياً للتأكد من الاتصال
- راقب `/auto_status` لمتابعة النشر التلقائي
- احتفظ بنسخة احتياطية من ملف .env
- استخدم `/diagnose_platforms` عند حدوث مشاكل

### 3. الأمان:
- لا تشارك رموز API مع أحد
- استخدم كلمات مرور قوية
- راجع صلاحيات البوت دورياً

## 🆕 الميزات القادمة

- [ ] دعم منصات إضافية (Twitter, LinkedIn)
- [ ] جدولة منشورات مخصصة
- [ ] تحليلات أداء مفصلة
- [ ] قوالب صور متعددة
- [ ] نشر متعدد اللغات

## 📞 الدعم

في حالة وجود مشاكل:
1. استخدم `/diagnose_platforms` للتشخيص التلقائي
2. راجع ملفات السجلات في `bot_controller.log`
3. تأكد من صحة إعدادات ملف .env
4. شغل `python test_bot_improvements.py` للاختبار الشامل

---

**تم تطوير هذه التحسينات بواسطة Augment Agent**  
**تاريخ التحديث: 2025-01-26**

🎉 **البوت الآن مستقل تماماً وجاهز للعمل!**
