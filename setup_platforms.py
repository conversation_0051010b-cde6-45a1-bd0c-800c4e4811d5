#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إعداد سريع للمنصات
"""

import os
import sys
from dotenv import set_key, load_dotenv

# إعداد الترميز للعربية
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# تحميل متغيرات البيئة الحالية
load_dotenv()

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🔧 أداة إعداد المنصات - روبوت نشر الآيات القرآنية")
    print("=" * 60)
    print("هذه الأداة ستساعدك في إعداد جميع المنصات بسهولة")
    print("=" * 60)

def get_user_input(prompt, current_value="", required=False, secret=False):
    """الحصول على إدخال من المستخدم"""
    if current_value:
        if secret:
            display_value = "*" * len(current_value)
        else:
            display_value = current_value
        full_prompt = f"{prompt} [{display_value}]: "
    else:
        full_prompt = f"{prompt}: "
    
    if required:
        full_prompt = f"🔴 {full_prompt}"
    else:
        full_prompt = f"🟡 {full_prompt}"
    
    value = input(full_prompt).strip()
    return value if value else current_value

def setup_telegram():
    """إعداد Telegram"""
    print("\n" + "="*50)
    print("📱 إعداد Telegram")
    print("="*50)
    
    current_token = os.getenv('TELEGRAM_BOT_TOKEN', '')
    current_channel = os.getenv('TELEGRAM_CHANNEL_ID', '')
    current_enabled = os.getenv('ENABLE_TELEGRAM', 'false').lower() == 'true'
    
    print(f"الحالة الحالية: {'✅ مفعل' if current_enabled else '❌ معطل'}")
    
    enable = input("هل تريد تفعيل Telegram؟ (y/n) [y]: ").lower()
    if enable in ['', 'y', 'yes', 'نعم']:
        print("\nللحصول على رمز البوت:")
        print("1. تحدث مع @BotFather على Telegram")
        print("2. استخدم الأمر /newbot")
        print("3. اتبع التعليمات واحفظ الرمز")
        print("-"*50)
        
        bot_token = get_user_input("رمز البوت", current_token, required=True, secret=True)
        channel_id = get_user_input("رابط أو معرف القناة (مثل: https://t.me/channel أو @channel)", current_channel, required=True)
        
        set_key('.env', 'ENABLE_TELEGRAM', 'true')
        set_key('.env', 'TELEGRAM_BOT_TOKEN', bot_token)
        set_key('.env', 'TELEGRAM_CHANNEL_ID', channel_id)
        print("✅ تم حفظ إعدادات Telegram")
    else:
        set_key('.env', 'ENABLE_TELEGRAM', 'false')
        print("⚪ تم تعطيل Telegram")

def setup_instagram():
    """إعداد Instagram"""
    print("\n" + "="*50)
    print("📸 إعداد Instagram")
    print("="*50)
    
    current_username = os.getenv('INSTAGRAM_USERNAME', '')
    current_password = os.getenv('INSTAGRAM_PASSWORD', '')
    current_enabled = os.getenv('ENABLE_INSTAGRAM', 'false').lower() == 'true'
    
    print(f"الحالة الحالية: {'✅ مفعل' if current_enabled else '❌ معطل'}")
    
    enable = input("هل تريد تفعيل Instagram؟ (y/n) [n]: ").lower()
    if enable in ['y', 'yes', 'نعم']:
        print("\nملاحظات مهمة:")
        print("- استخدم حساب ليس محمي بالمصادقة الثنائية")
        print("- قد يطلب Instagram التحقق أحياناً")
        print("- يُنصح بإنشاء حساب منفصل للبوت")
        print("-"*50)
        
        username = get_user_input("اسم المستخدم", current_username, required=True)
        password = get_user_input("كلمة المرور", current_password, required=True, secret=True)
        
        set_key('.env', 'ENABLE_INSTAGRAM', 'true')
        set_key('.env', 'INSTAGRAM_USERNAME', username)
        set_key('.env', 'INSTAGRAM_PASSWORD', password)
        print("✅ تم حفظ إعدادات Instagram")
    else:
        set_key('.env', 'ENABLE_INSTAGRAM', 'false')
        print("⚪ تم تعطيل Instagram")

def setup_facebook():
    """إعداد Facebook"""
    print("\n" + "="*50)
    print("📘 إعداد Facebook")
    print("="*50)
    
    current_token = os.getenv('FACEBOOK_ACCESS_TOKEN', '')
    current_page_id = os.getenv('FACEBOOK_PAGE_ID', '')
    current_enabled = os.getenv('ENABLE_FACEBOOK', 'false').lower() == 'true'
    
    print(f"الحالة الحالية: {'✅ مفعل' if current_enabled else '❌ معطل'}")
    
    enable = input("هل تريد تفعيل Facebook؟ (y/n) [n]: ").lower()
    if enable in ['y', 'yes', 'نعم']:
        print("\nللحصول على رمز الوصول:")
        print("1. اذهب إلى: https://developers.facebook.com/")
        print("2. أنشئ تطبيق جديد")
        print("3. احصل على Page Access Token")
        print("4. احصل على معرف الصفحة من إعدادات الصفحة")
        print("-"*50)
        
        access_token = get_user_input("رمز الوصول", current_token, required=True, secret=True)
        page_id = get_user_input("معرف الصفحة", current_page_id, required=True)
        
        set_key('.env', 'ENABLE_FACEBOOK', 'true')
        set_key('.env', 'FACEBOOK_ACCESS_TOKEN', access_token)
        set_key('.env', 'FACEBOOK_PAGE_ID', page_id)
        print("✅ تم حفظ إعدادات Facebook")
    else:
        set_key('.env', 'ENABLE_FACEBOOK', 'false')
        print("⚪ تم تعطيل Facebook")

def setup_gemini():
    """إعداد Gemini AI"""
    print("\n" + "="*50)
    print("🤖 إعداد Gemini AI (اختياري لكن مُوصى به)")
    print("="*50)
    
    current_key = os.getenv('GEMINI_API_KEY', '')
    
    print("Gemini AI يحسن جودة العبارات الإلهامية")
    
    enable = input("هل تريد إعداد Gemini AI؟ (y/n) [y]: ").lower()
    if enable in ['', 'y', 'yes', 'نعم']:
        print("\nللحصول على مفتاح مجاني:")
        print("1. اذهب إلى: https://makersuite.google.com/app/apikey")
        print("2. سجل دخول بحساب Google")
        print("3. اضغط 'Create API Key'")
        print("4. انسخ المفتاح وألصقه هنا")
        print("-"*50)
        
        api_key = get_user_input("مفتاح Gemini API", current_key, secret=True)
        
        if api_key:
            set_key('.env', 'GEMINI_API_KEY', api_key)
            print("✅ تم حفظ مفتاح Gemini API")
        else:
            print("⚪ تم تخطي Gemini AI")
    else:
        print("⚪ تم تخطي Gemini AI")

def setup_general_settings():
    """إعداد الإعدادات العامة"""
    print("\n" + "="*50)
    print("⚙️ الإعدادات العامة")
    print("="*50)
    
    current_interval = os.getenv('PUBLISH_INTERVAL_HOURS', '4')
    
    interval = get_user_input("فترة النشر التلقائي (بالساعات)", current_interval)
    
    try:
        interval_int = int(interval)
        if 1 <= interval_int <= 24:
            set_key('.env', 'PUBLISH_INTERVAL_HOURS', str(interval_int))
            print(f"✅ تم تعيين فترة النشر إلى {interval_int} ساعة")
        else:
            print("⚠️ يجب أن تكون الفترة بين 1-24 ساعة، تم استخدام القيمة الافتراضية")
    except ValueError:
        print("⚠️ قيمة غير صحيحة، تم استخدام القيمة الافتراضية")

def test_setup():
    """اختبار الإعدادات"""
    print("\n" + "="*50)
    print("🧪 اختبار الإعدادات")
    print("="*50)
    
    test = input("هل تريد اختبار الإعدادات؟ (y/n) [y]: ").lower()
    
    if test in ['', 'y', 'yes', 'نعم']:
        print("جاري اختبار الإعدادات...")
        
        try:
            import subprocess
            result = subprocess.run(['python', 'test_app.py'], 
                                  capture_output=True, text=True, timeout=60)
            
            print("\n📋 نتائج الاختبار:")
            print("-"*30)
            
            # عرض النتائج المهمة فقط
            lines = result.stdout.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['✅', '❌', '⚠️', 'نجح', 'فشل', 'معطل']):
                    print(f"  {line}")
                    
        except subprocess.TimeoutExpired:
            print("⏰ انتهت مهلة الاختبار")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")

def show_summary():
    """عرض ملخص الإعدادات"""
    print("\n" + "="*60)
    print("📋 ملخص الإعدادات")
    print("="*60)
    
    # إعادة تحميل المتغيرات
    load_dotenv()
    
    platforms = []
    if os.getenv('ENABLE_TELEGRAM', 'false').lower() == 'true':
        platforms.append('Telegram')
    if os.getenv('ENABLE_INSTAGRAM', 'false').lower() == 'true':
        platforms.append('Instagram')
    if os.getenv('ENABLE_FACEBOOK', 'false').lower() == 'true':
        platforms.append('Facebook')
    
    print(f"📱 المنصات المفعلة: {', '.join(platforms) if platforms else 'لا توجد'}")
    print(f"🤖 Gemini AI: {'✅ مفعل' if os.getenv('GEMINI_API_KEY') else '❌ معطل'}")
    print(f"⏰ فترة النشر: {os.getenv('PUBLISH_INTERVAL_HOURS', '4')} ساعة")
    
    if platforms:
        print("\n✅ الإعداد مكتمل! يمكنك الآن تشغيل البوت:")
        print("   python main.py")
    else:
        print("\n⚠️ لم يتم تفعيل أي منصة. يرجى إعداد منصة واحدة على الأقل.")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التأكد من وجود ملف .env
    if not os.path.exists('.env'):
        print("📝 إنشاء ملف الإعدادات...")
        with open('.env', 'w', encoding='utf-8') as f:
            f.write("# إعدادات روبوت نشر الآيات القرآنية\n")
            f.write("SECRET_KEY=my-secret-key-for-development\n")
            f.write("DEBUG=False\n")
            f.write("ENVIRONMENT=production\n")
            f.write("PORT=5000\n")
    
    try:
        # إعداد المكونات
        setup_telegram()
        setup_instagram()
        setup_facebook()
        setup_gemini()
        setup_general_settings()
        
        # اختبار الإعدادات
        test_setup()
        
        # عرض الملخص
        show_summary()
        
        print("\n" + "="*60)
        print("🎉 تم الانتهاء من الإعداد!")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")

if __name__ == "__main__":
    main()
