#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة ويب بسيطة لإعداد البيانات المطلوبة
"""

from flask import Flask, render_template_string, request, redirect, url_for, flash
import os
from dotenv import load_dotenv, set_key

# تحميل متغيرات البيئة
load_dotenv()

app = Flask(__name__)
app.secret_key = 'setup-secret-key'

# قالب HTML لصفحة الإعداد
SETUP_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد روبوت نشر الآيات القرآنية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .setup-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            padding: 40px;
            max-width: 800px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header .icon {
            font-size: 4rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
        }
        
        .section-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid #e0e0e0;
        }
        
        .section-title {
            color: #333;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-left: 10px;
            color: #667eea;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-save {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 40px;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .help-text {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }
        
        .required {
            color: #dc3545;
        }
        
        .optional {
            color: #28a745;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .toggle-section {
            cursor: pointer;
            user-select: none;
        }
        
        .toggle-section:hover {
            background: #e9ecef;
            border-radius: 10px;
            padding: 10px;
            margin: -10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="header">
                <div class="icon">
                    <i class="fas fa-mosque"></i>
                </div>
                <h1>إعداد روبوت نشر الآيات القرآنية</h1>
                <p class="text-muted">أدخل البيانات المطلوبة لتشغيل الروبوت</p>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }}">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST">
                <!-- إعدادات Gemini AI -->
                <div class="section-card">
                    <div class="section-title">
                        <i class="fas fa-robot"></i>
                        ذكاء اصطناعي Gemini <span class="required">(مطلوب)</span>
                    </div>
                    <div class="mb-3">
                        <label for="gemini_api_key" class="form-label">مفتاح Gemini API</label>
                        <input type="text" class="form-control" id="gemini_api_key" name="gemini_api_key" 
                               value="{{ current_values.get('GEMINI_API_KEY', '') }}" 
                               placeholder="أدخل مفتاح Gemini API">
                        <div class="help-text">
                            <i class="fas fa-info-circle"></i>
                            احصل على مفتاح مجاني من: <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                        </div>
                    </div>
                </div>
                
                <!-- إعدادات Telegram -->
                <div class="section-card">
                    <div class="section-title toggle-section" onclick="toggleSection('telegram')">
                        <i class="fas fa-paper-plane"></i>
                        Telegram <span class="optional">(اختياري)</span>
                        <i class="fas fa-chevron-down ms-auto" id="telegram-icon"></i>
                    </div>
                    <div id="telegram-section" style="display: none;">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable_telegram" name="enable_telegram" 
                                       {{ 'checked' if current_values.get('ENABLE_TELEGRAM', '').lower() == 'true' else '' }}>
                                <label class="form-check-label" for="enable_telegram">
                                    تفعيل النشر على Telegram
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="telegram_bot_token" class="form-label">رمز البوت</label>
                            <input type="text" class="form-control" id="telegram_bot_token" name="telegram_bot_token" 
                                   value="{{ current_values.get('TELEGRAM_BOT_TOKEN', '') }}" 
                                   placeholder="123456789:ABCdefGHIjklMNOpqrsTUVwxyz">
                            <div class="help-text">
                                <i class="fas fa-info-circle"></i>
                                أنشئ بوت جديد عبر @BotFather على Telegram
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="telegram_channel_id" class="form-label">معرف القناة</label>
                            <input type="text" class="form-control" id="telegram_channel_id" name="telegram_channel_id" 
                                   value="{{ current_values.get('TELEGRAM_CHANNEL_ID', '') }}" 
                                   placeholder="@channel_username">
                            <div class="help-text">
                                <i class="fas fa-info-circle"></i>
                                أضف البوت كمشرف في القناة
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إعدادات Instagram -->
                <div class="section-card">
                    <div class="section-title toggle-section" onclick="toggleSection('instagram')">
                        <i class="fab fa-instagram"></i>
                        Instagram <span class="optional">(اختياري)</span>
                        <i class="fas fa-chevron-down ms-auto" id="instagram-icon"></i>
                    </div>
                    <div id="instagram-section" style="display: none;">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable_instagram" name="enable_instagram" 
                                       {{ 'checked' if current_values.get('ENABLE_INSTAGRAM', '').lower() == 'true' else '' }}>
                                <label class="form-check-label" for="enable_instagram">
                                    تفعيل النشر على Instagram
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="instagram_username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="instagram_username" name="instagram_username" 
                                   value="{{ current_values.get('INSTAGRAM_USERNAME', '') }}" 
                                   placeholder="your_username">
                        </div>
                        <div class="mb-3">
                            <label for="instagram_password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="instagram_password" name="instagram_password" 
                                   value="{{ current_values.get('INSTAGRAM_PASSWORD', '') }}" 
                                   placeholder="your_password">
                            <div class="help-text">
                                <i class="fas fa-exclamation-triangle"></i>
                                تأكد من عدم تفعيل المصادقة الثنائية
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إعدادات Facebook -->
                <div class="section-card">
                    <div class="section-title toggle-section" onclick="toggleSection('facebook')">
                        <i class="fab fa-facebook"></i>
                        Facebook <span class="optional">(اختياري)</span>
                        <i class="fas fa-chevron-down ms-auto" id="facebook-icon"></i>
                    </div>
                    <div id="facebook-section" style="display: none;">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable_facebook" name="enable_facebook" 
                                       {{ 'checked' if current_values.get('ENABLE_FACEBOOK', '').lower() == 'true' else '' }}>
                                <label class="form-check-label" for="enable_facebook">
                                    تفعيل النشر على Facebook
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="facebook_access_token" class="form-label">رمز الوصول</label>
                            <input type="text" class="form-control" id="facebook_access_token" name="facebook_access_token" 
                                   value="{{ current_values.get('FACEBOOK_ACCESS_TOKEN', '') }}" 
                                   placeholder="EAABwzLixnjYBAO...">
                            <div class="help-text">
                                <i class="fas fa-info-circle"></i>
                                احصل على رمز الوصول من Facebook Developers
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="facebook_page_id" class="form-label">معرف الصفحة</label>
                            <input type="text" class="form-control" id="facebook_page_id" name="facebook_page_id" 
                                   value="{{ current_values.get('FACEBOOK_PAGE_ID', '') }}" 
                                   placeholder="123456789012345">
                        </div>
                    </div>
                </div>
                
                <!-- أزرار الحفظ والاختبار -->
                <div class="text-center">
                    <button type="submit" name="action" value="save" class="btn btn-save">
                        <i class="fas fa-save me-2"></i>
                        حفظ الإعدادات
                    </button>
                    <button type="submit" name="action" value="test" class="btn btn-test">
                        <i class="fas fa-vial me-2"></i>
                        اختبار الإعدادات
                    </button>
                </div>
                
                <div class="text-center mt-4">
                    <a href="/run_app" class="btn btn-success">
                        <i class="fas fa-play me-2"></i>
                        تشغيل التطبيق الرئيسي
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleSection(sectionName) {
            const section = document.getElementById(sectionName + '-section');
            const icon = document.getElementById(sectionName + '-icon');
            
            if (section.style.display === 'none') {
                section.style.display = 'block';
                icon.className = 'fas fa-chevron-up ms-auto';
            } else {
                section.style.display = 'none';
                icon.className = 'fas fa-chevron-down ms-auto';
            }
        }
        
        // إظهار الأقسام المفعلة
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('enable_telegram').checked) {
                toggleSection('telegram');
            }
            if (document.getElementById('enable_instagram').checked) {
                toggleSection('instagram');
            }
            if (document.getElementById('enable_facebook').checked) {
                toggleSection('facebook');
            }
        });
    </script>
</body>
</html>
"""

def get_current_env_values():
    """الحصول على القيم الحالية من ملف .env"""
    return {
        'GEMINI_API_KEY': os.getenv('GEMINI_API_KEY', ''),
        'ENABLE_TELEGRAM': os.getenv('ENABLE_TELEGRAM', 'false'),
        'TELEGRAM_BOT_TOKEN': os.getenv('TELEGRAM_BOT_TOKEN', ''),
        'TELEGRAM_CHANNEL_ID': os.getenv('TELEGRAM_CHANNEL_ID', ''),
        'ENABLE_INSTAGRAM': os.getenv('ENABLE_INSTAGRAM', 'false'),
        'INSTAGRAM_USERNAME': os.getenv('INSTAGRAM_USERNAME', ''),
        'INSTAGRAM_PASSWORD': os.getenv('INSTAGRAM_PASSWORD', ''),
        'ENABLE_FACEBOOK': os.getenv('ENABLE_FACEBOOK', 'false'),
        'FACEBOOK_ACCESS_TOKEN': os.getenv('FACEBOOK_ACCESS_TOKEN', ''),
        'FACEBOOK_PAGE_ID': os.getenv('FACEBOOK_PAGE_ID', ''),
    }

def save_to_env_file(data):
    """حفظ البيانات في ملف .env"""
    env_file = '.env'
    
    # تحديث القيم
    for key, value in data.items():
        set_key(env_file, key, value)

@app.route('/')
def setup():
    """صفحة الإعداد الرئيسية"""
    current_values = get_current_env_values()
    return render_template_string(SETUP_TEMPLATE, current_values=current_values)

@app.route('/', methods=['POST'])
def save_setup():
    """حفظ الإعدادات"""
    action = request.form.get('action')
    
    # جمع البيانات من النموذج
    data = {
        'GEMINI_API_KEY': request.form.get('gemini_api_key', '').strip(),
        'ENABLE_TELEGRAM': 'true' if request.form.get('enable_telegram') else 'false',
        'TELEGRAM_BOT_TOKEN': request.form.get('telegram_bot_token', '').strip(),
        'TELEGRAM_CHANNEL_ID': request.form.get('telegram_channel_id', '').strip(),
        'ENABLE_INSTAGRAM': 'true' if request.form.get('enable_instagram') else 'false',
        'INSTAGRAM_USERNAME': request.form.get('instagram_username', '').strip(),
        'INSTAGRAM_PASSWORD': request.form.get('instagram_password', '').strip(),
        'ENABLE_FACEBOOK': 'true' if request.form.get('enable_facebook') else 'false',
        'FACEBOOK_ACCESS_TOKEN': request.form.get('facebook_access_token', '').strip(),
        'FACEBOOK_PAGE_ID': request.form.get('facebook_page_id', '').strip(),
    }
    
    if action == 'save':
        # حفظ البيانات
        save_to_env_file(data)
        flash('تم حفظ الإعدادات بنجاح!', 'success')
        
    elif action == 'test':
        # اختبار الإعدادات
        save_to_env_file(data)
        
        # تشغيل اختبار سريع
        import subprocess
        try:
            result = subprocess.run(['python', 'test_app.py'], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                flash('تم اختبار الإعدادات بنجاح!', 'success')
            else:
                flash('هناك بعض المشاكل في الإعدادات. راجع وحدة التحكم للتفاصيل.', 'warning')
        except Exception as e:
            flash(f'خطأ في اختبار الإعدادات: {str(e)}', 'danger')
    
    return redirect(url_for('setup'))

@app.route('/run_app')
def run_app():
    """تشغيل التطبيق الرئيسي"""
    import subprocess
    import threading
    
    def run_main_app():
        subprocess.run(['python', 'app.py'])
    
    # تشغيل التطبيق في خيط منفصل
    app_thread = threading.Thread(target=run_main_app, daemon=True)
    app_thread.start()
    
    flash('تم تشغيل التطبيق الرئيسي! يمكنك الوصول إليه على http://localhost:5000', 'success')
    return redirect(url_for('setup'))

if __name__ == '__main__':
    print("🌐 تشغيل واجهة الإعداد...")
    print("📱 افتح المتصفح واذهب إلى: http://localhost:3000")
    print("🔧 أدخل البيانات المطلوبة واحفظ الإعدادات")
    print("✅ ثم اضغط 'تشغيل التطبيق الرئيسي'")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=3000, debug=True)
