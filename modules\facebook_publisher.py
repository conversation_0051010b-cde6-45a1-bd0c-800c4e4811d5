import os
import requests
from typing import Optional, Dict

class FacebookPublisher:
    """ناشر المحتوى على Facebook"""
    
    def __init__(self, access_token: Optional[str] = None, page_id: Optional[str] = None):
        self.access_token = access_token or os.getenv('FACEBOOK_ACCESS_TOKEN')
        self.page_id = page_id or os.getenv('FACEBOOK_PAGE_ID')
        
        if not self.access_token:
            raise ValueError("رمز الوصول لـ Facebook مطلوب")
        if not self.page_id:
            raise ValueError("معرف صفحة Facebook مطلوب")
        
        self.base_url = "https://graph.facebook.com/v18.0"
    
    def upload_photo(self, image_path: str, caption: str) -> bool:
        """رفع صورة إلى صفحة Facebook"""
        try:
            # التحقق من وجود الصورة
            if not os.path.exists(image_path):
                print(f"الصورة غير موجودة: {image_path}")
                return False
            
            # رفع الصورة
            url = f"{self.base_url}/{self.page_id}/photos"
            
            with open(image_path, 'rb') as image_file:
                files = {'source': image_file}
                data = {
                    'message': caption,
                    'access_token': self.access_token
                }
                
                response = requests.post(url, files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                post_id = result.get('post_id', result.get('id'))
                print(f"تم نشر الصورة بنجاح على Facebook: {post_id}")
                return True
            else:
                error_info = response.json() if response.content else "خطأ غير معروف"
                print(f"فشل في نشر الصورة على Facebook: {error_info}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"خطأ في الشبكة أثناء النشر على Facebook: {e}")
            return False
        except Exception as e:
            print(f"خطأ غير متوقع في Facebook: {e}")
            return False
    
    def post_text(self, message: str) -> bool:
        """نشر منشور نصي على صفحة Facebook"""
        try:
            url = f"{self.base_url}/{self.page_id}/feed"
            data = {
                'message': message,
                'access_token': self.access_token
            }
            
            response = requests.post(url, data=data)
            
            if response.status_code == 200:
                result = response.json()
                post_id = result.get('id')
                print(f"تم نشر المنشور النصي بنجاح على Facebook: {post_id}")
                return True
            else:
                error_info = response.json() if response.content else "خطأ غير معروف"
                print(f"فشل في نشر المنشور النصي على Facebook: {error_info}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"خطأ في الشبكة أثناء النشر على Facebook: {e}")
            return False
        except Exception as e:
            print(f"خطأ غير متوقع في Facebook: {e}")
            return False
    
    def publish_verse_content(self, image_path: str, content: Dict) -> bool:
        """نشر محتوى الآية على Facebook"""
        verse_data = content['verse']
        phrase = content['phrase']
        caption = content.get('caption', '')
        
        # إنشاء تعليق مناسب لـ Facebook
        if not caption:
            caption = self.format_verse_caption(verse_data, phrase)
        
        return self.upload_photo(image_path, caption)
    
    def format_verse_caption(self, verse_data: Dict, phrase: str) -> str:
        """تنسيق تعليق مناسب لـ Facebook"""
        caption = f"""🕌 {phrase}

📖 "{verse_data['text']}"

📚 سورة {verse_data['surah']} - آية {verse_data['verse_number']}

#قرآن #ذكر #دعاء #أمل #طمأنينة #إسلام #آيات_قرآنية #تذكير #روحانيات #سكينة #إيمان #تدبر #هداية #نور #بركة #رحمة #مغفرة #توبة #صبر #شكر #حمد #تسبيح #استغفار #دعوة #خير #بر #تقوى #ورع #زهد #عبادة #طاعة #محبة #رضا #قناعة #يقين #ثقة #أمان #سلام #فرح #سرور #بشرى #خير #بركات #رزق #فضل #كرم #جود #عطاء #منة #نعمة"""
        
        return caption
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بـ Facebook"""
        try:
            url = f"{self.base_url}/{self.page_id}"
            params = {
                'fields': 'name,id',
                'access_token': self.access_token
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                page_info = response.json()
                print(f"تم الاتصال بنجاح مع صفحة: {page_info.get('name')}")
                return True
            else:
                error_info = response.json() if response.content else "خطأ غير معروف"
                print(f"فشل في الاتصال مع Facebook: {error_info}")
                return False
                
        except Exception as e:
            print(f"خطأ في اختبار الاتصال مع Facebook: {e}")
            return False
    
    def get_page_info(self) -> Optional[Dict]:
        """الحصول على معلومات الصفحة"""
        try:
            url = f"{self.base_url}/{self.page_id}"
            params = {
                'fields': 'name,id,followers_count,fan_count',
                'access_token': self.access_token
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"خطأ في الحصول على معلومات الصفحة: {e}")
            return None
