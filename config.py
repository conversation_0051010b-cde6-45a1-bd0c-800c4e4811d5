import os
import sys
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعداد الترميز للعربية
if sys.platform.startswith('win'):
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'Arabic_Saudi Arabia.1256')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
        except:
            pass

# إعداد الترميز للطباعة
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

class Config:
    """إعدادات التطبيق"""
    
    # إعدادات عامة
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    PORT = int(os.getenv('PORT', 5000))
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')
    
    # إعدادات Gemini API
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    
    # إعدادات Telegram
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    TELEGRAM_CHANNEL_ID = os.getenv('TELEGRAM_CHANNEL_ID')
    
    # إعدادات Instagram
    INSTAGRAM_USERNAME = os.getenv('INSTAGRAM_USERNAME')
    INSTAGRAM_PASSWORD = os.getenv('INSTAGRAM_PASSWORD')
    
    # إعدادات Facebook
    FACEBOOK_ACCESS_TOKEN = os.getenv('FACEBOOK_ACCESS_TOKEN')
    FACEBOOK_PAGE_ID = os.getenv('FACEBOOK_PAGE_ID')
    
    # إعدادات المجلدات
    BACKGROUNDS_DIR = 'backgrounds'
    FONTS_DIR = 'fonts'
    GENERATED_IMAGES_DIR = 'generated_images'
    DATA_DIR = 'data'
    
    # إعدادات الجدولة
    PUBLISH_INTERVAL_HOURS = int(os.getenv('PUBLISH_INTERVAL_HOURS', 4))
    
    # إعدادات المنصات المفعلة
    ENABLE_TELEGRAM = os.getenv('ENABLE_TELEGRAM', 'True').lower() == 'true'
    ENABLE_INSTAGRAM = os.getenv('ENABLE_INSTAGRAM', 'True').lower() == 'true'
    ENABLE_FACEBOOK = os.getenv('ENABLE_FACEBOOK', 'True').lower() == 'true'
    
    # إعدادات الصور
    IMAGE_WIDTH = int(os.getenv('IMAGE_WIDTH', 1080))
    IMAGE_HEIGHT = int(os.getenv('IMAGE_HEIGHT', 1080))
    IMAGE_QUALITY = int(os.getenv('IMAGE_QUALITY', 95))
    
    # إعدادات الخطوط
    VERSE_FONT_SIZE = int(os.getenv('VERSE_FONT_SIZE', 48))
    SURAH_FONT_SIZE = int(os.getenv('SURAH_FONT_SIZE', 32))
    PHRASE_FONT_SIZE = int(os.getenv('PHRASE_FONT_SIZE', 36))
    
    @classmethod
    def validate_config(cls):
        """التحقق من صحة الإعدادات"""
        errors = []
        
        # التحقق من مفاتيح API المطلوبة
        if not cls.GEMINI_API_KEY:
            errors.append("GEMINI_API_KEY مطلوب")
        
        if cls.ENABLE_TELEGRAM:
            if not cls.TELEGRAM_BOT_TOKEN:
                errors.append("TELEGRAM_BOT_TOKEN مطلوب عند تفعيل Telegram")
            if not cls.TELEGRAM_CHANNEL_ID:
                errors.append("TELEGRAM_CHANNEL_ID مطلوب عند تفعيل Telegram")
        
        if cls.ENABLE_INSTAGRAM:
            if not cls.INSTAGRAM_USERNAME:
                errors.append("INSTAGRAM_USERNAME مطلوب عند تفعيل Instagram")
            if not cls.INSTAGRAM_PASSWORD:
                errors.append("INSTAGRAM_PASSWORD مطلوب عند تفعيل Instagram")
        
        if cls.ENABLE_FACEBOOK:
            if not cls.FACEBOOK_ACCESS_TOKEN:
                errors.append("FACEBOOK_ACCESS_TOKEN مطلوب عند تفعيل Facebook")
            if not cls.FACEBOOK_PAGE_ID:
                errors.append("FACEBOOK_PAGE_ID مطلوب عند تفعيل Facebook")
        
        return errors
    
    @classmethod
    def get_enabled_platforms(cls):
        """الحصول على قائمة المنصات المفعلة"""
        platforms = []
        
        if cls.ENABLE_TELEGRAM:
            platforms.append('telegram')
        if cls.ENABLE_INSTAGRAM:
            platforms.append('instagram')
        if cls.ENABLE_FACEBOOK:
            platforms.append('facebook')
        
        return platforms
    
    @classmethod
    def print_config_summary(cls):
        """طباعة ملخص الإعدادات"""
        print("=" * 50)
        print("ملخص إعدادات التطبيق")
        print("=" * 50)
        print(f"البيئة: {cls.ENVIRONMENT}")
        print(f"المنفذ: {cls.PORT}")
        print(f"فترة النشر: {cls.PUBLISH_INTERVAL_HOURS} ساعات")
        print(f"المنصات المفعلة: {', '.join(cls.get_enabled_platforms())}")
        print(f"أبعاد الصورة: {cls.IMAGE_WIDTH}x{cls.IMAGE_HEIGHT}")
        print("=" * 50)


class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    PUBLISH_INTERVAL_HOURS = 1  # نشر كل ساعة للاختبار


class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    PUBLISH_INTERVAL_HOURS = 4  # نشر كل 4 ساعات


class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    DEBUG = True
    PUBLISH_INTERVAL_HOURS = 0.1  # نشر كل 6 دقائق للاختبار السريع


# اختيار الإعدادات حسب البيئة
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig
}

def get_config():
    """الحصول على إعدادات البيئة الحالية"""
    env = os.getenv('ENVIRONMENT', 'development')
    return config_map.get(env, DevelopmentConfig)
