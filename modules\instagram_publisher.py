import os
import time
import logging
from instagrapi import Client
from instagrapi.exceptions import LoginRequired, PleaseWaitFewMinutes, ChallengeRequired, TwoFactorRequired
from typing import Optional, Dict

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class InstagramPublisher:
    """ناشر المحتوى على Instagram"""
    
    def __init__(self, username: Optional[str] = None, password: Optional[str] = None):
        self.username = username or os.getenv('INSTAGRAM_USERNAME')
        self.password = password or os.getenv('INSTAGRAM_PASSWORD')
        
        if not self.username or not self.password:
            raise ValueError("اسم المستخدم وكلمة المرور لـ Instagram مطلوبان")
        
        self.client = Client()
        self.is_logged_in = False
        
        # إعدادات الأمان
        self.session_file = "instagram_session.json"
        
    def login(self) -> bool:
        """تسجيل الدخول إلى Instagram"""
        try:
            # محاولة تحميل جلسة محفوظة
            if os.path.exists(self.session_file):
                try:
                    self.client.load_settings(self.session_file)
                    # التحقق من صحة الجلسة
                    user_info = self.client.account_info()
                    if user_info:
                        self.is_logged_in = True
                        logger.info("تم تسجيل الدخول باستخدام الجلسة المحفوظة")
                        return True
                except Exception as e:
                    logger.warning(f"فشل في استخدام الجلسة المحفوظة: {e}")
                    # حذف الجلسة التالفة
                    try:
                        os.remove(self.session_file)
                    except:
                        pass

            # تسجيل دخول جديد
            logger.info("محاولة تسجيل دخول جديد...")
            self.client.login(self.username, self.password)

            # حفظ الجلسة
            self.client.dump_settings(self.session_file)
            self.is_logged_in = True
            logger.info("تم تسجيل الدخول بنجاح إلى Instagram")
            return True

        except LoginRequired as e:
            logger.error(f"خطأ في تسجيل الدخول: بيانات غير صحيحة - {e}")
            return False

        except TwoFactorRequired as e:
            logger.error("مطلوب تفعيل المصادقة الثنائية")
            return False

        except ChallengeRequired as e:
            logger.error("Instagram يطلب تحدي أمني")
            return False

        except PleaseWaitFewMinutes as e:
            logger.warning(f"Instagram يطلب الانتظار: {e}")
            return False
        except Exception as e:
            print(f"خطأ في تسجيل الدخول إلى Instagram: {e}")
            return False
    
    def upload_photo(self, image_path: str, caption: str) -> bool:
        """رفع صورة إلى Instagram"""
        if not self.is_logged_in:
            if not self.login():
                return False
        
        try:
            # التحقق من وجود الصورة
            if not os.path.exists(image_path):
                print(f"الصورة غير موجودة: {image_path}")
                return False
            
            # رفع الصورة
            media = self.client.photo_upload(
                path=image_path,
                caption=caption
            )
            
            if media:
                print(f"تم نشر الصورة بنجاح على Instagram: {media.pk}")
                return True
            else:
                print("فشل في رفع الصورة إلى Instagram")
                return False
                
        except PleaseWaitFewMinutes:
            print("Instagram يطلب الانتظار، سيتم المحاولة لاحقاً")
            return False
        except Exception as e:
            print(f"خطأ في رفع الصورة إلى Instagram: {e}")
            return False
    
    def publish_verse_content(self, image_path: str, content: Dict) -> bool:
        """نشر محتوى الآية على Instagram"""
        verse_data = content['verse']
        phrase = content['phrase']
        caption = content.get('caption', '')
        
        # إنشاء تعليق مناسب لـ Instagram
        if not caption:
            caption = self.format_verse_caption(verse_data, phrase)
        
        # تحديد طول التعليق (Instagram يحد من 2200 حرف)
        if len(caption) > 2200:
            caption = caption[:2200] + "..."
        
        return self.upload_photo(image_path, caption)
    
    def format_verse_caption(self, verse_data: Dict, phrase: str) -> str:
        """تنسيق تعليق مناسب لـ Instagram"""
        caption = f"""🕌 {phrase}

📖 "{verse_data['text']}"

📚 سورة {verse_data['surah']} - آية {verse_data['verse_number']}

#قرآن #ذكر #دعاء #أمل #طمأنينة #إسلام #آيات_قرآنية #تذكير #روحانيات #سكينة #إيمان #تدبر #هداية #نور #بركة #رحمة #مغفرة #توبة #صبر #شكر #حمد #تسبيح #استغفار #دعوة #خير #بر #تقوى #ورع #زهد #عبادة #طاعة #محبة #رضا #قناعة #يقين #ثقة #أمان #سلام #فرح #سرور #بشرى #خير #بركات #رزق #فضل #كرم #جود #عطاء #منة #نعمة"""
        
        return caption
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بـ Instagram"""
        try:
            if self.login():
                user_info = self.client.account_info()
                logger.info(f"تم الاتصال بنجاح مع حساب: {user_info.username}")
                return True
            return False
        except Exception as e:
            logger.error(f"فشل في الاتصال مع Instagram: {e}")
            return False

    def diagnose_issues(self) -> Dict[str, any]:
        """تشخيص مشاكل النشر على Instagram"""
        diagnosis = {
            'credentials_valid': False,
            'login_successful': False,
            'account_accessible': False,
            'issues': [],
            'recommendations': []
        }

        # فحص بيانات الاعتماد
        if not self.username or not self.password:
            diagnosis['issues'].append("اسم المستخدم أو كلمة المرور مفقودة")
            diagnosis['recommendations'].append("تحقق من INSTAGRAM_USERNAME و INSTAGRAM_PASSWORD في ملف .env")
            return diagnosis

        diagnosis['credentials_valid'] = True

        try:
            # محاولة تسجيل الدخول
            if self.login():
                diagnosis['login_successful'] = True

                # فحص معلومات الحساب
                try:
                    user_info = self.client.account_info()
                    diagnosis['account_accessible'] = True
                    diagnosis['account_info'] = {
                        'username': user_info.username,
                        'full_name': user_info.full_name,
                        'follower_count': user_info.follower_count,
                        'following_count': user_info.following_count,
                        'is_private': user_info.is_private,
                        'is_business': user_info.is_business
                    }

                    # تحذيرات
                    if user_info.is_private:
                        diagnosis['recommendations'].append("الحساب خاص، قد يؤثر على الوصول")

                except Exception as e:
                    diagnosis['issues'].append(f"لا يمكن الوصول لمعلومات الحساب: {e}")

            else:
                diagnosis['issues'].append("فشل في تسجيل الدخول")
                diagnosis['recommendations'].append("تحقق من صحة اسم المستخدم وكلمة المرور")

        except TwoFactorRequired:
            diagnosis['issues'].append("مطلوب تفعيل المصادقة الثنائية")
            diagnosis['recommendations'].append("قم بتعطيل المصادقة الثنائية أو استخدم App Password")

        except ChallengeRequired:
            diagnosis['issues'].append("Instagram يطلب تحدي أمني")
            diagnosis['recommendations'].append("قم بتسجيل الدخول من المتصفح وأكمل التحدي")

        except PleaseWaitFewMinutes:
            diagnosis['issues'].append("Instagram يطلب الانتظار بسبب كثرة المحاولات")
            diagnosis['recommendations'].append("انتظر بضع دقائق قبل المحاولة مرة أخرى")

        except Exception as e:
            diagnosis['issues'].append(f"خطأ غير متوقع: {e}")
            diagnosis['recommendations'].append("تحقق من اتصال الإنترنت وحاول مرة أخرى")

        return diagnosis
    
    def logout(self):
        """تسجيل الخروج"""
        try:
            if self.is_logged_in:
                self.client.logout()
                self.is_logged_in = False
                print("تم تسجيل الخروج من Instagram")
        except Exception as e:
            print(f"خطأ في تسجيل الخروج: {e}")
    
    def __del__(self):
        """تنظيف الموارد عند حذف الكائن"""
        self.logout()
