// Dashboard JavaScript Functions
let performanceChart = null;
let autoPublishEnabled = false;
let currentInterval = 3; // Default 3 hours

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    loadPlatformStatus();
    loadRecentPosts();
    loadContentInsights();
    initializePerformanceChart();
    loadAutoPublishStatus();

    // Auto-refresh every 5 minutes
    setInterval(refreshDashboard, 300000);
});

// Initialize dashboard
function initializeDashboard() {
    console.log('Dashboard initialized');
    showNotification('مرحباً بك في لوحة التحكم', 'success');
}

// Load platform status
async function loadPlatformStatus() {
    try {
        const response = await fetch('/api/platform-status');
        const data = await response.json();
        
        const platformStatusDiv = document.getElementById('platformStatus');
        platformStatusDiv.innerHTML = '';
        
        const platforms = [
            { name: 'Telegram', icon: 'fab fa-telegram-plane', key: 'telegram' },
            { name: 'Instagram', icon: 'fab fa-instagram', key: 'instagram' },
            { name: 'Facebook', icon: 'fab fa-facebook', key: 'facebook' }
        ];
        
        platforms.forEach(platform => {
            const status = data[platform.key] || { status: 'inactive', posts: 0 };
            const statusClass = status.status === 'active' ? 'active' : 'inactive';
            
            const platformDiv = document.createElement('div');
            platformDiv.className = `platform-status ${statusClass}`;
            platformDiv.innerHTML = `
                <div class="platform-icon">
                    <i class="${platform.icon}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-bold">${platform.name}</div>
                    <div class="small">${status.posts} منشور</div>
                </div>
                <div class="text-end">
                    <i class="fas fa-circle"></i>
                </div>
            `;
            
            platformStatusDiv.appendChild(platformDiv);
        });
        
    } catch (error) {
        console.error('Error loading platform status:', error);
        showNotification('خطأ في تحميل حالة المنصات', 'error');
    }
}

// Load recent posts
async function loadRecentPosts() {
    try {
        const response = await fetch('/api/recent-posts');
        const data = await response.json();
        
        const recentPostsDiv = document.getElementById('recentPosts');
        recentPostsDiv.innerHTML = '';
        
        if (data.posts && data.posts.length > 0) {
            data.posts.forEach(post => {
                const performanceClass = getPerformanceClass(post.performance_score);
                const performanceText = getPerformanceText(post.performance_score);
                
                const postDiv = document.createElement('div');
                postDiv.className = 'post-item';
                postDiv.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${post.surah} - آية ${post.verse_number}</h6>
                            <p class="text-muted small mb-2">${post.phrase}</p>
                            <small class="text-muted">${formatDate(post.timestamp)}</small>
                        </div>
                        <span class="post-performance ${performanceClass}">
                            ${performanceText}
                        </span>
                    </div>
                    <div class="d-flex gap-2">
                        ${post.success_platforms.map(platform => 
                            `<span class="badge bg-success">${platform}</span>`
                        ).join('')}
                    </div>
                `;
                
                recentPostsDiv.appendChild(postDiv);
            });
        } else {
            recentPostsDiv.innerHTML = '<p class="text-muted text-center">لا توجد منشورات حديثة</p>';
        }
        
    } catch (error) {
        console.error('Error loading recent posts:', error);
        showNotification('خطأ في تحميل المنشورات الأخيرة', 'error');
    }
}

// Load content insights
async function loadContentInsights() {
    try {
        const response = await fetch('/api/content-insights');
        const data = await response.json();
        
        const insightsDiv = document.getElementById('contentInsights');
        insightsDiv.innerHTML = '';
        
        if (data.theme_insights) {
            const themesDiv = document.createElement('div');
            themesDiv.innerHTML = '<h6 class="mb-3">أفضل المواضيع</h6>';
            
            Object.entries(data.theme_insights)
                .sort(([,a], [,b]) => b.avg_performance - a.avg_performance)
                .slice(0, 5)
                .forEach(([theme, stats]) => {
                    const themeDiv = document.createElement('div');
                    themeDiv.className = 'mb-3 p-3 bg-light rounded';
                    themeDiv.innerHTML = `
                        <div class="d-flex justify-content-between">
                            <span class="fw-bold">${theme}</span>
                            <span class="badge bg-primary">${stats.avg_performance}</span>
                        </div>
                        <small class="text-muted">${stats.posts_count} منشور</small>
                    `;
                    themesDiv.appendChild(themeDiv);
                });
            
            insightsDiv.appendChild(themesDiv);
        }
        
        if (data.ai_vs_manual) {
            const aiDiv = document.createElement('div');
            aiDiv.innerHTML = `
                <h6 class="mb-3 mt-4">الذكاء الاصطناعي مقابل اليدوي</h6>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center p-3 bg-primary text-white rounded">
                            <div class="h4">${data.ai_vs_manual.ai_generated.avg_performance}</div>
                            <div class="small">AI المولد</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-secondary text-white rounded">
                            <div class="h4">${data.ai_vs_manual.manual.avg_performance}</div>
                            <div class="small">يدوي</div>
                        </div>
                    </div>
                </div>
            `;
            insightsDiv.appendChild(aiDiv);
        }
        
    } catch (error) {
        console.error('Error loading content insights:', error);
        showNotification('خطأ في تحميل رؤى المحتوى', 'error');
    }
}

// Initialize performance chart
async function initializePerformanceChart() {
    try {
        const response = await fetch('/api/performance-data');
        const data = await response.json();
        
        const ctx = document.getElementById('performanceChart').getContext('2d');
        
        if (performanceChart) {
            performanceChart.destroy();
        }
        
        performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels || [],
                datasets: [{
                    label: 'نقاط الأداء',
                    data: data.performance_scores || [],
                    borderColor: 'rgb(102, 126, 234)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });
        
    } catch (error) {
        console.error('Error initializing performance chart:', error);
    }
}

// Publish now function
async function publishNow() {
    showLoading(true);
    
    try {
        const response = await fetch('/api/publish-now', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('تم النشر بنجاح!', 'success');
            refreshDashboard();
        } else {
            showNotification('فشل في النشر: ' + result.message, 'error');
        }
        
    } catch (error) {
        console.error('Error publishing:', error);
        showNotification('خطأ في النشر', 'error');
    } finally {
        showLoading(false);
    }
}

// Preview post function
async function previewPost() {
    showLoading(true);
    
    try {
        const response = await fetch('/api/preview-post');
        const result = await response.json();
        
        if (result.success) {
            // Show preview modal or open in new window
            window.open(result.preview_url, '_blank');
        } else {
            showNotification('فشل في إنشاء المعاينة', 'error');
        }
        
    } catch (error) {
        console.error('Error creating preview:', error);
        showNotification('خطأ في إنشاء المعاينة', 'error');
    } finally {
        showLoading(false);
    }
}

// Open content manager
function openContentManager() {
    // Redirect to content management page
    window.location.href = '/content-manager';
}

// Export report function
async function exportReport() {
    showLoading(true);
    
    try {
        const response = await fetch('/api/export-report', {
            method: 'POST'
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analytics_report_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            showNotification('تم تصدير التقرير بنجاح', 'success');
        } else {
            showNotification('فشل في تصدير التقرير', 'error');
        }
        
    } catch (error) {
        console.error('Error exporting report:', error);
        showNotification('خطأ في تصدير التقرير', 'error');
    } finally {
        showLoading(false);
    }
}

// Refresh dashboard
function refreshDashboard() {
    loadPlatformStatus();
    loadRecentPosts();
    loadContentInsights();
    initializePerformanceChart();
    
    // Update stats
    fetch('/api/dashboard-stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('totalPosts').textContent = data.total_posts || 0;
            document.getElementById('successRate').textContent = (data.success_rate || 0) + '%';
            document.getElementById('avgEngagement').textContent = data.avg_engagement || 0;
            document.getElementById('nextPost').textContent = data.next_post_time || 'قريباً';
        })
        .catch(error => console.error('Error refreshing stats:', error));
}

// Utility functions
function getPerformanceClass(score) {
    if (score >= 80) return 'performance-excellent';
    if (score >= 60) return 'performance-good';
    if (score >= 40) return 'performance-average';
    return 'performance-poor';
}

function getPerformanceText(score) {
    if (score >= 80) return 'ممتاز';
    if (score >= 60) return 'جيد';
    if (score >= 40) return 'متوسط';
    return 'ضعيف';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

function showLoading(show) {
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    if (show) {
        modal.show();
    } else {
        modal.hide();
    }
}

// Auto-publish functions
async function loadAutoPublishStatus() {
    try {
        const response = await fetch('/api/scheduler-status');
        const data = await response.json();

        autoPublishEnabled = data.is_running || false;
        currentInterval = data.interval_hours || 3;

        updateAutoPublishButton();

    } catch (error) {
        console.error('Error loading auto-publish status:', error);
    }
}

function updateAutoPublishButton() {
    const btn = document.getElementById('autoPublishBtn');
    const icon = document.getElementById('autoPublishIcon');
    const text = document.getElementById('autoPublishText');

    if (autoPublishEnabled) {
        btn.className = 'btn btn-danger w-100';
        icon.className = 'fas fa-stop me-2';
        text.textContent = 'إيقاف النشر التلقائي';
    } else {
        btn.className = 'btn btn-success w-100';
        icon.className = 'fas fa-play me-2';
        text.textContent = 'تشغيل النشر التلقائي';
    }
}

async function toggleAutoPublish() {
    try {
        showLoading(true);

        const action = autoPublishEnabled ? 'stop' : 'start';
        const response = await fetch('/api/toggle-auto-publish', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: action, interval_hours: currentInterval })
        });

        const result = await response.json();

        if (result.success) {
            autoPublishEnabled = !autoPublishEnabled;
            updateAutoPublishButton();
            showNotification(result.message, 'success');

            // Refresh dashboard stats
            refreshDashboard();
        } else {
            showNotification(result.message || 'حدث خطأ', 'error');
        }

    } catch (error) {
        console.error('Error toggling auto-publish:', error);
        showNotification('خطأ في تغيير حالة النشر التلقائي', 'error');
    } finally {
        showLoading(false);
    }
}

function openScheduleSettings() {
    // Set current interval in the modal
    document.getElementById('publishInterval').value = currentInterval;
    updateSchedulePreview();

    const modal = new bootstrap.Modal(document.getElementById('scheduleModal'));
    modal.show();

    // Add event listener for interval change
    document.getElementById('publishInterval').addEventListener('change', updateSchedulePreview);
}

function updateSchedulePreview() {
    const interval = parseInt(document.getElementById('publishInterval').value);
    const preview = document.getElementById('schedulePreview');

    let previewText = '';
    if (interval === 1) {
        previewText = 'سيتم النشر كل ساعة';
    } else if (interval < 24) {
        previewText = `سيتم النشر كل ${interval} ساعات`;
    } else {
        previewText = 'سيتم النشر مرة واحدة يومياً';
    }

    preview.textContent = previewText;
}

async function saveScheduleSettings() {
    try {
        const interval = parseInt(document.getElementById('publishInterval').value);
        const immediateStart = document.getElementById('immediateStart').checked;

        showLoading(true);

        const response = await fetch('/api/update-schedule', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                interval_hours: interval,
                immediate_start: immediateStart
            })
        });

        const result = await response.json();

        if (result.success) {
            currentInterval = interval;
            showNotification('تم حفظ إعدادات التوقيت بنجاح', 'success');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleModal'));
            modal.hide();

            // Refresh dashboard
            refreshDashboard();
        } else {
            showNotification(result.message || 'فشل في حفظ الإعدادات', 'error');
        }

    } catch (error) {
        console.error('Error saving schedule settings:', error);
        showNotification('خطأ في حفظ إعدادات التوقيت', 'error');
    } finally {
        showLoading(false);
    }
}

async function checkSchedulerStatus() {
    try {
        showLoading(true);

        const response = await fetch('/api/scheduler-status');
        const data = await response.json();

        const statusContent = document.getElementById('schedulerStatusContent');

        let statusHtml = `
            <div class="row">
                <div class="col-md-6">
                    <h6>حالة النظام</h6>
                    <p class="mb-2">
                        <span class="badge ${data.is_running ? 'bg-success' : 'bg-danger'}">
                            ${data.is_running ? 'يعمل' : 'متوقف'}
                        </span>
                    </p>
                </div>
                <div class="col-md-6">
                    <h6>فترة النشر</h6>
                    <p class="mb-2">${data.interval_hours} ساعات</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h6>آخر نشر</h6>
                    <p class="mb-2">${data.last_run ? formatDate(data.last_run) : 'لم يتم النشر بعد'}</p>
                </div>
                <div class="col-md-6">
                    <h6>النشر التالي</h6>
                    <p class="mb-2">${data.time_until_next_run_seconds > 0 ? formatTimeRemaining(data.time_until_next_run_seconds) : 'قريباً'}</p>
                </div>
            </div>
        `;

        statusContent.innerHTML = statusHtml;

        const modal = new bootstrap.Modal(document.getElementById('statusModal'));
        modal.show();

    } catch (error) {
        console.error('Error checking scheduler status:', error);
        showNotification('خطأ في جلب حالة الجدولة', 'error');
    } finally {
        showLoading(false);
    }
}

function formatTimeRemaining(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
        return `${hours}س ${minutes}د`;
    } else {
        return `${minutes}د`;
    }
}
