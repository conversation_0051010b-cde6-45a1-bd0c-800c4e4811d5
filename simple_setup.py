#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إعداد بسيطة لإدخال البيانات المطلوبة
"""

import os
from dotenv import set_key

def get_user_input(prompt, default="", required=False):
    """الحصول على إدخال من المستخدم"""
    if default:
        full_prompt = f"{prompt} [{default}]: "
    else:
        full_prompt = f"{prompt}: "
    
    if required:
        full_prompt = f"🔴 {full_prompt}"
    else:
        full_prompt = f"🟡 {full_prompt}"
    
    value = input(full_prompt).strip()
    return value if value else default

def setup_gemini():
    """إعداد Gemini API"""
    print("\n" + "="*50)
    print("🤖 إعداد Gemini AI (مطلوب)")
    print("="*50)
    print("للحصول على مفتاح مجاني:")
    print("1. اذهب إلى: https://makersuite.google.com/app/apikey")
    print("2. سجل دخول بحساب Google")
    print("3. اضغط 'Create API Key'")
    print("4. انسخ المفتاح وألصقه هنا")
    print("-"*50)
    
    api_key = get_user_input("مفتاح Gemini API", required=True)
    
    if api_key:
        set_key('.env', 'GEMINI_API_KEY', api_key)
        print("✅ تم حفظ مفتاح Gemini API")
        return True
    else:
        print("❌ مفتاح Gemini API مطلوب!")
        return False

def setup_telegram():
    """إعداد Telegram (اختياري)"""
    print("\n" + "="*50)
    print("📱 إعداد Telegram (اختياري)")
    print("="*50)
    
    enable = input("هل تريد تفعيل النشر على Telegram؟ (y/n) [n]: ").lower()
    
    if enable in ['y', 'yes', 'نعم']:
        print("\nللحصول على رمز البوت:")
        print("1. تحدث مع @BotFather على Telegram")
        print("2. استخدم الأمر /newbot")
        print("3. اتبع التعليمات واحفظ الرمز")
        print("-"*50)
        
        bot_token = get_user_input("رمز البوت")
        channel_id = get_user_input("رابط أو معرف القناة (مثل: https://t.me/channel_name أو @channel_name)")
        
        set_key('.env', 'ENABLE_TELEGRAM', 'true')
        set_key('.env', 'TELEGRAM_BOT_TOKEN', bot_token)
        set_key('.env', 'TELEGRAM_CHANNEL_ID', channel_id)
        print("✅ تم حفظ إعدادات Telegram")
    else:
        set_key('.env', 'ENABLE_TELEGRAM', 'false')
        print("⚪ تم تعطيل Telegram")

def setup_instagram():
    """إعداد Instagram (اختياري)"""
    print("\n" + "="*50)
    print("📸 إعداد Instagram (اختياري)")
    print("="*50)
    
    enable = input("هل تريد تفعيل النشر على Instagram؟ (y/n) [n]: ").lower()
    
    if enable in ['y', 'yes', 'نعم']:
        print("\nملاحظات مهمة:")
        print("- استخدم حساب ليس محمي بالمصادقة الثنائية")
        print("- قد يطلب Instagram التحقق أحياناً")
        print("-"*50)
        
        username = get_user_input("اسم المستخدم")
        password = get_user_input("كلمة المرور")
        
        set_key('.env', 'ENABLE_INSTAGRAM', 'true')
        set_key('.env', 'INSTAGRAM_USERNAME', username)
        set_key('.env', 'INSTAGRAM_PASSWORD', password)
        print("✅ تم حفظ إعدادات Instagram")
    else:
        set_key('.env', 'ENABLE_INSTAGRAM', 'false')
        print("⚪ تم تعطيل Instagram")

def setup_facebook():
    """إعداد Facebook (اختياري)"""
    print("\n" + "="*50)
    print("📘 إعداد Facebook (اختياري)")
    print("="*50)
    
    enable = input("هل تريد تفعيل النشر على Facebook؟ (y/n) [n]: ").lower()
    
    if enable in ['y', 'yes', 'نعم']:
        print("\nللحصول على رمز الوصول:")
        print("1. اذهب إلى: https://developers.facebook.com/")
        print("2. أنشئ تطبيق جديد")
        print("3. احصل على Page Access Token")
        print("-"*50)
        
        access_token = get_user_input("رمز الوصول")
        page_id = get_user_input("معرف الصفحة")
        
        set_key('.env', 'ENABLE_FACEBOOK', 'true')
        set_key('.env', 'FACEBOOK_ACCESS_TOKEN', access_token)
        set_key('.env', 'FACEBOOK_PAGE_ID', page_id)
        print("✅ تم حفظ إعدادات Facebook")
    else:
        set_key('.env', 'ENABLE_FACEBOOK', 'false')
        print("⚪ تم تعطيل Facebook")

def test_setup():
    """اختبار الإعدادات"""
    print("\n" + "="*50)
    print("🧪 اختبار الإعدادات")
    print("="*50)
    
    test = input("هل تريد اختبار الإعدادات؟ (y/n) [y]: ").lower()
    
    if test in ['', 'y', 'yes', 'نعم']:
        print("جاري اختبار الإعدادات...")
        
        import subprocess
        try:
            result = subprocess.run(['python', 'test_app.py'], 
                                  capture_output=True, text=True, timeout=60)
            
            print("\n📋 نتائج الاختبار:")
            print("-"*30)
            
            if "جميع الاختبارات نجحت" in result.stdout:
                print("✅ جميع الإعدادات تعمل بنجاح!")
            elif "فشل في" in result.stdout:
                print("⚠️ بعض الإعدادات تحتاج مراجعة")
                print("💡 يمكنك المتابعة والتطبيق سيعمل بالإعدادات المتاحة")
            else:
                print("ℹ️ تم الاختبار - راجع التفاصيل أدناه")
            
            # عرض الأخطاء المهمة فقط
            lines = result.stdout.split('\n')
            for line in lines:
                if 'مطلوب' in line or 'فشل' in line or 'خطأ' in line:
                    print(f"  {line}")
                    
        except subprocess.TimeoutExpired:
            print("⏰ انتهت مهلة الاختبار - يمكنك المتابعة")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")

def main():
    """الدالة الرئيسية"""
    print("🕌 مرحباً بك في إعداد روبوت نشر الآيات القرآنية")
    print("="*60)
    print("سنقوم بإعداد البيانات المطلوبة خطوة بخطوة")
    print("="*60)
    
    # التأكد من وجود ملف .env
    if not os.path.exists('.env'):
        print("📝 إنشاء ملف الإعدادات...")
        with open('.env', 'w', encoding='utf-8') as f:
            f.write("# إعدادات روبوت نشر الآيات القرآنية\n")
            f.write("SECRET_KEY=my-secret-key-for-development\n")
            f.write("DEBUG=True\n")
            f.write("ENVIRONMENT=development\n")
            f.write("PORT=5000\n")
            f.write("PUBLISH_INTERVAL_HOURS=4\n")
    
    # إعداد المكونات
    success = True
    
    # 1. إعداد Gemini (مطلوب)
    if not setup_gemini():
        success = False
    
    # 2. إعداد المنصات (اختياري)
    setup_telegram()
    setup_instagram()
    setup_facebook()
    
    # 3. اختبار الإعدادات
    test_setup()
    
    # 4. النتيجة النهائية
    print("\n" + "="*60)
    if success:
        print("🎉 تم الإعداد بنجاح!")
        print("✅ يمكنك الآن تشغيل التطبيق:")
        print("   python app.py")
        print("\n🌐 ثم اذهب إلى: http://localhost:5000")
        print("📱 بيانات تسجيل الدخول الافتراضية:")
        print("   المستخدم: admin")
        print("   كلمة المرور: admin123")
    else:
        print("⚠️ يرجى إضافة مفتاح Gemini API على الأقل")
        print("🔄 يمكنك إعادة تشغيل الإعداد: python simple_setup.py")
    
    print("="*60)
    
    # خيار تشغيل التطبيق مباشرة
    if success:
        run_now = input("\nهل تريد تشغيل التطبيق الآن؟ (y/n) [y]: ").lower()
        if run_now in ['', 'y', 'yes', 'نعم']:
            print("🚀 تشغيل التطبيق...")
            import subprocess
            subprocess.run(['python', 'app.py'])

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        print("🔄 يمكنك إعادة المحاولة: python simple_setup.py")
