services:
  - type: web
    name: quran-verses-bot
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn app:app --bind 0.0.0.0:$PORT --workers 1 --timeout 120
    plan: free
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: PORT
        value: 10000
      - key: PUBLISH_INTERVAL_HOURS
        value: 4
      - key: ENABLE_TELEGRAM
        value: true
      - key: ENABLE_INSTAGRAM
        value: true
      - key: ENABLE_FACEBOOK
        value: true
