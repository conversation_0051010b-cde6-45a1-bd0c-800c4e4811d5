#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل بوت التحكم المحسن
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# إعداد الترميز للعربية
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_controller.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من ملف .env
    if not os.path.exists('.env'):
        print("❌ ملف .env غير موجود!")
        print("💡 قم بإنشاء ملف .env وإضافة الإعدادات المطلوبة")
        return False
    
    # التحقق من المجلدات المطلوبة
    required_dirs = ['data', 'backgrounds', 'fonts', 'generated_images', 'modules']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"📁 إنشاء مجلد {dir_name}...")
            os.makedirs(dir_name, exist_ok=True)
    
    # التحقق من الوحدات المطلوبة
    try:
        import telegram
        import dotenv
        from PIL import Image
        print("✅ جميع الوحدات المطلوبة متوفرة")
    except ImportError as e:
        print(f"❌ وحدة مفقودة: {e}")
        print("💡 قم بتشغيل: pip install -r requirements.txt")
        return False
    
    return True

def show_config_status():
    """عرض حالة الإعدادات"""
    print("\n📋 حالة الإعدادات:")
    print("=" * 40)
    
    try:
        from modules.config_manager import ConfigManager
        config_manager = ConfigManager()
        
        # عرض ملخص الإعدادات
        summary = config_manager.get_config_summary()
        print(summary)
        
        # التحقق من صحة الإعدادات
        validation = config_manager.validate_config()
        
        if validation['errors']:
            print("\n❌ يجب إصلاح الأخطاء التالية قبل التشغيل:")
            for error in validation['errors']:
                print(f"  • {error}")
            return False
        
        if validation['warnings']:
            print("\n⚠️ تحذيرات:")
            for warning in validation['warnings']:
                print(f"  • {warning}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الإعدادات: {e}")
        return False

def run_bot():
    """تشغيل البوت"""
    try:
        print("\n🤖 بدء تشغيل بوت التحكم...")
        print("=" * 50)

        from telegram_bot_controller import TelegramBotController

        # إنشاء وتشغيل البوت
        bot_controller = TelegramBotController()

        print("✅ تم تهيئة البوت بنجاح")
        print("🚀 البوت يعمل الآن...")
        print("📱 يمكنك التفاعل معه عبر Telegram")
        print("⏹️ اضغط Ctrl+C لإيقاف البوت")
        print("=" * 50)

        # تشغيل البوت
        bot_controller.run()

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في تشغيل البوت: {e}")
        print(f"\n❌ خطأ في تشغيل البوت: {e}")
        print("💡 راجع ملف bot_controller.log للمزيد من التفاصيل")

def main():
    """الدالة الرئيسية"""
    print("🕌 بوت نشر الآيات القرآنية - نظام التحكم المحسن")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("\n❌ فشل في التحقق من المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # عرض حالة الإعدادات
    if not show_config_status():
        print("\n❌ يجب إصلاح الإعدادات قبل التشغيل")
        print("💡 استخدم البوت لتعديل الإعدادات أو راجع ملف .env")
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل البوت
    try:
        run_bot()
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
