# دليل إدارة الإعدادات المحسن

## نظرة عامة

تم تحسين بوت التحكم ليعرض الإعدادات الحقيقية من ملف `.env` بدلاً من الإعدادات الوهمية، مع إمكانية تعديلها مباشرة من خلال البوت.

## الميزات الجديدة

### 1. عرض الإعدادات الحقيقية
- يعرض البوت الآن الإعدادات الفعلية من ملف `.env`
- إخفاء البيانات الحساسة (كلمات المرور، مفاتيح API)
- عرض حالة كل منصة (مفعلة/معطلة/غير مُعدة)

### 2. تعديل الإعدادات من البوت
يمكنك الآن تعديل جميع الإعدادات مباشرة من البوت باستخدام الأوامر التالية:

#### إعدادات Telegram:
```
/set_enable_telegram true/false
/set_telegram_bot_token <رمز_البوت>
/set_telegram_channel_id <معرف_القناة>
```

#### إعدادات Instagram:
```
/set_enable_instagram true/false
/set_instagram_username <اسم_المستخدم>
/set_instagram_password <كلمة_المرور>
```

#### إعدادات Facebook:
```
/set_enable_facebook true/false
/set_facebook_access_token <رمز_الوصول>
/set_facebook_page_id <معرف_الصفحة>
```

#### إعدادات Gemini AI:
```
/set_gemini_api_key <مفتاح_API>
```

### 3. ملخص الإعدادات
- أمر `/config_summary` لعرض ملخص شامل للإعدادات
- التحقق من صحة الإعدادات وعرض الأخطاء والتحذيرات
- عرض المنصات المفعلة والمعطلة

### 4. تاريخ التغييرات
- أمر `/config_history` لعرض آخر 10 تغييرات في الإعدادات
- تسجيل من قام بالتغيير ومتى
- إخفاء القيم الحساسة في التاريخ

### 5. إعادة تحميل الإعدادات
- زر "إعادة تحميل الإعدادات" لتطبيق التغييرات فوراً
- إعادة تهيئة المكونات تلقائياً بعد التحديث

## كيفية الاستخدام

### 1. الوصول لإدارة الإعدادات
1. ابدأ البوت بـ `/start`
2. اضغط على "📱 إدارة المنصات"
3. اضغط على "⚙️ إعداد المنصات"

### 2. عرض الإعدادات الحالية
- ستظهر لك جميع الإعدادات الحقيقية
- البيانات الحساسة مخفية جزئياً للأمان
- حالة كل منصة واضحة (مفعلة/معطلة/غير مُعدة)

### 3. تعديل الإعدادات
#### الطريقة الأولى - الأزرار:
1. اضغط على زر المنصة المراد تعديلها
2. ستظهر لك الأوامر المطلوبة
3. استخدم الأوامر في الدردشة

#### الطريقة الثانية - الأوامر المباشرة:
```
/set_enable_telegram true
/set_telegram_bot_token 123456:ABC-DEF...
/set_telegram_channel_id @mychannel
```

### 4. التحقق من التغييرات
1. اضغط على "🔄 إعادة تحميل الإعدادات"
2. اضغط على "📋 ملخص الإعدادات" للتحقق
3. اضغط على "📜 تاريخ التغييرات" لرؤية السجل

## مثال عملي

### إعداد Telegram من الصفر:
```
/set_enable_telegram true
/set_telegram_bot_token **********************************************
/set_telegram_channel_id @mychannel
```

ثم اضغط على "🔄 إعادة تحميل الإعدادات"

### إعداد Instagram:
```
/set_enable_instagram true
/set_instagram_username myusername
/set_instagram_password mypassword
```

### إعداد Gemini AI:
```
/set_gemini_api_key AIzaSyA9sCmQsOZIPKRKX14aMJsC8Mt7IFPsYE8
```

## الأمان

### حماية البيانات الحساسة:
- كلمات المرور ومفاتيح API مخفية في العرض
- تسجيل التغييرات بدون كشف القيم الحساسة
- النسخ الاحتياطية محفوظة في `data/config_history.json`

### التحقق من الصلاحيات:
- جميع أوامر التعديل تتطلب صلاحيات إدارية
- تسجيل معرف المستخدم مع كل تغيير

## استكشاف الأخطاء

### إذا لم تظهر التغييرات:
1. تأكد من استخدام "🔄 إعادة تحميل الإعدادات"
2. تحقق من ملف `.env` يدوياً
3. راجع سجلات البوت للأخطاء

### إذا فشل التحديث:
1. تحقق من صلاحيات الكتابة على ملف `.env`
2. تأكد من صحة تنسيق القيم المدخلة
3. راجع `data/config_history.json` للتأكد من التسجيل

### رسائل الخطأ الشائعة:
- "❌ فشل في تحديث الإعدادات" - مشكلة في الكتابة على الملف
- "❌ القيمة يجب أن تكون true أو false" - قيمة خاطئة للإعدادات المنطقية
- "❌ يرجى تحديد..." - معامل مفقود في الأمر

## الملفات المتأثرة

- `telegram_bot_controller.py` - البوت الرئيسي
- `modules/config_manager.py` - مدير الإعدادات الجديد
- `.env` - ملف الإعدادات
- `data/config_history.json` - تاريخ التغييرات

## نصائح للاستخدام الأمثل

1. **استخدم ملخص الإعدادات بانتظام** للتأكد من صحة الإعدادات
2. **راجع تاريخ التغييرات** عند حدوث مشاكل
3. **اختبر المنصات** بعد كل تغيير مهم
4. **احتفظ بنسخة احتياطية** من ملف `.env` قبل التغييرات الكبيرة
5. **استخدم إعادة التحميل** بعد كل مجموعة تغييرات

## الدعم

إذا واجهت أي مشاكل:
1. راجع سجلات البوت في `bot_controller.log`
2. تحقق من ملف `data/config_history.json`
3. استخدم `/config_summary` للتشخيص
4. تأكد من صحة تنسيق ملف `.env`
